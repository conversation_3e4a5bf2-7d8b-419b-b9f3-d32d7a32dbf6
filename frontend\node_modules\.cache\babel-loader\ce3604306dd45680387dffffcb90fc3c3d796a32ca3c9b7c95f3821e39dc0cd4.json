{"ast": null, "code": "!function (e, t) {\n  \"object\" == typeof exports && \"object\" == typeof module ? module.exports = t(require(\"react\")) : \"function\" == typeof define && define.amd ? define(\"lib\", [\"react\"], t) : \"object\" == typeof exports ? exports.lib = t(require(\"react\")) : e.lib = t(e.react);\n}(\"undefined\" != typeof self ? self : this, function (e) {\n  return function () {\n    \"use strict\";\n\n    var t = {\n        155: function (t) {\n          t.exports = e;\n        }\n      },\n      o = {};\n    function n(e) {\n      var r = o[e];\n      if (void 0 !== r) return r.exports;\n      var s = o[e] = {\n        exports: {}\n      };\n      return t[e](s, s.exports, n), s.exports;\n    }\n    n.d = function (e, t) {\n      for (var o in t) n.o(t, o) && !n.o(e, o) && Object.defineProperty(e, o, {\n        enumerable: !0,\n        get: t[o]\n      });\n    }, n.o = function (e, t) {\n      return Object.prototype.hasOwnProperty.call(e, t);\n    }, n.r = function (e) {\n      \"undefined\" != typeof Symbol && Symbol.toStringTag && Object.defineProperty(e, Symbol.toStringTag, {\n        value: \"Module\"\n      }), Object.defineProperty(e, \"__esModule\", {\n        value: !0\n      });\n    };\n    var r = {};\n    n.r(r), n.d(r, {\n      useReactToPrint: function () {\n        return f;\n      }\n    });\n    var s = n(155);\n    function i({\n      level: e = \"error\",\n      messages: t,\n      suppressErrors: o = !1\n    }) {\n      o || (\"error\" === e ? console.error(t) : \"warning\" === e ? console.warn(t) : console.debug(t));\n    }\n    function l(e, t) {\n      if (t || !e) {\n        const e = document.getElementById(\"printWindow\");\n        e && document.body.removeChild(e);\n      }\n    }\n    function a(e) {\n      return e instanceof Error ? e : new Error(\"Unknown Error\");\n    }\n    function c(e, t) {\n      const {\n        documentTitle: o,\n        onAfterPrint: n,\n        onPrintError: r,\n        preserveAfterPrint: s,\n        print: c,\n        suppressErrors: d\n      } = t;\n      setTimeout(() => {\n        var t, u;\n        if (e.contentWindow) {\n          function p() {\n            null == n || n(), l(s);\n          }\n          if (e.contentWindow.focus(), c) c(e).then(p).catch(e => {\n            r ? r(\"print\", a(e)) : i({\n              messages: [\"An error was thrown by the specified `print` function\"],\n              suppressErrors: d\n            });\n          });else {\n            if (e.contentWindow.print) {\n              const h = null !== (u = null === (t = e.contentDocument) || void 0 === t ? void 0 : t.title) && void 0 !== u ? u : \"\",\n                f = e.ownerDocument.title;\n              o && (e.ownerDocument.title = o, e.contentDocument && (e.contentDocument.title = o)), e.contentWindow.print(), o && (e.ownerDocument.title = f, e.contentDocument && (e.contentDocument.title = h));\n            } else i({\n              messages: [\"Printing for this browser is not currently possible: the browser does not have a `print` method available for iframes.\"],\n              suppressErrors: d\n            });\n            [/Android/i, /webOS/i, /iPhone/i, /iPad/i, /iPod/i, /BlackBerry/i, /Windows Phone/i].some(e => {\n              var t, o;\n              return (null !== (o = null !== (t = navigator.userAgent) && void 0 !== t ? t : navigator.vendor) && void 0 !== o ? o : \"opera\" in window && window.opera).match(e);\n            }) ? setTimeout(p, 500) : p();\n          }\n        } else i({\n          messages: [\"Printing failed because the `contentWindow` of the print iframe did not load. This is possibly an error with `react-to-print`. Please file an issue: https://github.com/MatthewHerbst/react-to-print/issues/\"],\n          suppressErrors: d\n        });\n      }, 500);\n    }\n    function d(e) {\n      const t = [],\n        o = document.createTreeWalker(e, NodeFilter.SHOW_ELEMENT, null);\n      let n = o.nextNode();\n      for (; n;) t.push(n), n = o.nextNode();\n      return t;\n    }\n    function u(e, t, o) {\n      const n = d(e),\n        r = d(t);\n      if (n.length === r.length) for (let e = 0; e < n.length; e++) {\n        const t = n[e],\n          s = r[e],\n          i = t.shadowRoot;\n        if (null !== i) {\n          const e = s.attachShadow({\n            mode: i.mode\n          });\n          e.innerHTML = i.innerHTML, u(i, e, o);\n        }\n      } else i({\n        messages: [\"When cloning shadow root content, source and target elements have different size. `onBeforePrint` likely resolved too early.\", e, t],\n        suppressErrors: o\n      });\n    }\n    const p = '\\n    @page {\\n        /* Remove browser default header (title) and footer (url) */\\n        margin: 0;\\n    }\\n    @media print {\\n        body {\\n            /* Tell browsers to print background colors */\\n            color-adjust: exact; /* Firefox. This is an older version of \"print-color-adjust\" */\\n            print-color-adjust: exact; /* Firefox/Safari */\\n            -webkit-print-color-adjust: exact; /* Chrome/Safari/Edge/Opera */\\n        }\\n    }\\n';\n    function h(e, t, o, n) {\n      var r, s, l, d, h;\n      const {\n          contentNode: f,\n          clonedContentNode: g,\n          clonedImgNodes: m,\n          clonedVideoNodes: b,\n          numResourcesToLoad: y,\n          originalCanvasNodes: v\n        } = o,\n        {\n          bodyClass: w,\n          fonts: E,\n          ignoreGlobalStyles: A,\n          pageStyle: T,\n          nonce: S,\n          suppressErrors: P,\n          copyShadowRoots: k\n        } = n;\n      e.onload = null;\n      const x = null !== (r = e.contentDocument) && void 0 !== r ? r : null === (s = e.contentWindow) || void 0 === s ? void 0 : s.document;\n      if (x) {\n        const o = x.body.appendChild(g);\n        k && u(f, o, !!P), E && ((null === (l = e.contentDocument) || void 0 === l ? void 0 : l.fonts) && (null === (d = e.contentWindow) || void 0 === d ? void 0 : d.FontFace) ? E.forEach(o => {\n          const n = new FontFace(o.family, o.source, {\n            weight: o.weight,\n            style: o.style\n          });\n          e.contentDocument.fonts.add(n), n.loaded.then(() => {\n            t(n);\n          }).catch(e => {\n            t(n, [\"Failed loading the font:\", n, \"Load error:\", a(e)]);\n          });\n        }) : (E.forEach(e => {\n          t(e);\n        }), i({\n          messages: ['\"react-to-print\" is not able to load custom fonts because the browser does not support the FontFace API but will continue attempting to print the page'],\n          suppressErrors: P\n        })));\n        const n = null != T ? T : p,\n          r = x.createElement(\"style\");\n        S && (r.setAttribute(\"nonce\", S), x.head.setAttribute(\"nonce\", S)), r.appendChild(x.createTextNode(n)), x.head.appendChild(r), w && x.body.classList.add(...w.split(\" \"));\n        const s = x.querySelectorAll(\"canvas\");\n        for (let e = 0; e < v.length; ++e) {\n          const t = v[e],\n            o = s[e];\n          if (void 0 === o) {\n            i({\n              messages: [\"A canvas element could not be copied for printing, has it loaded? `onBeforePrint` likely resolved too early.\", t],\n              suppressErrors: P\n            });\n            continue;\n          }\n          const n = o.getContext(\"2d\");\n          n && n.drawImage(t, 0, 0);\n        }\n        for (let e = 0; e < m.length; e++) {\n          const o = m[e],\n            n = o.getAttribute(\"src\");\n          if (n) {\n            const e = new Image();\n            e.onload = () => {\n              t(o);\n            }, e.onerror = (e, n, r, s, i) => {\n              t(o, [\"Error loading <img>\", o, \"Error\", i]);\n            }, e.src = n;\n          } else t(o, ['Found an <img> tag with an empty \"src\" attribute. This prevents pre-loading it.', o]);\n        }\n        for (let e = 0; e < b.length; e++) {\n          const o = b[e];\n          o.preload = \"auto\";\n          const n = o.getAttribute(\"poster\");\n          if (n) {\n            const e = new Image();\n            e.onload = () => {\n              t(o);\n            }, e.onerror = (e, r, s, i, l) => {\n              t(o, [\"Error loading video poster\", n, \"for video\", o, \"Error:\", l]);\n            }, e.src = n;\n          } else o.readyState >= 2 ? t(o) : o.src ? (o.onloadeddata = () => {\n            t(o);\n          }, o.onerror = (e, n, r, s, i) => {\n            t(o, [\"Error loading video\", o, \"Error\", i]);\n          }, o.onstalled = () => {\n            t(o, [\"Loading video stalled, skipping\", o]);\n          }) : t(o, [\"Error loading video, `src` is empty\", o]);\n        }\n        const c = \"select\",\n          y = f.querySelectorAll(c),\n          C = x.querySelectorAll(c);\n        for (let e = 0; e < y.length; e++) C[e].value = y[e].value;\n        if (!A) {\n          const e = document.querySelectorAll(\"style, link[rel~='stylesheet'], link[as='style']\");\n          for (let o = 0, n = e.length; o < n; ++o) {\n            const n = e[o];\n            if (\"style\" === n.tagName.toLowerCase()) {\n              const e = x.createElement(n.tagName),\n                t = n.sheet;\n              if (t) {\n                let r = \"\";\n                try {\n                  const e = t.cssRules.length;\n                  for (let o = 0; o < e; ++o) \"string\" == typeof t.cssRules[o].cssText && (r += `${t.cssRules[o].cssText}\\r\\n`);\n                } catch (e) {\n                  i({\n                    messages: [\"A stylesheet could not be accessed. This is likely due to the stylesheet having cross-origin imports, and many browsers block script access to cross-origin stylesheets. See https://github.com/MatthewHerbst/react-to-print/issues/429 for details. You may be able to load the sheet by both marking the stylesheet with the cross `crossorigin` attribute, and setting the `Access-Control-Allow-Origin` header on the server serving the stylesheet. Alternatively, host the stylesheet on your domain to avoid this issue entirely.\", n, `Original error: ${a(e).message}`],\n                    level: \"warning\"\n                  });\n                }\n                e.setAttribute(\"id\", `react-to-print-${o}`), S && e.setAttribute(\"nonce\", S), e.appendChild(x.createTextNode(r)), x.head.appendChild(e);\n              }\n            } else if (n.getAttribute(\"href\")) {\n              if (n.hasAttribute(\"disabled\")) i({\n                messages: [\"`react-to-print` encountered a <link> tag with a `disabled` attribute and will ignore it. Note that the `disabled` attribute is deprecated, and some browsers ignore it. You should stop using it. https://developer.mozilla.org/en-US/docs/Web/HTML/Element/link#attr-disabled. The <link> is:\", n],\n                level: \"warning\"\n              }), t(n);else {\n                const e = x.createElement(n.tagName);\n                for (let t = 0, o = n.attributes.length; t < o; ++t) {\n                  const o = n.attributes[t];\n                  o && e.setAttribute(o.nodeName, null !== (h = o.nodeValue) && void 0 !== h ? h : \"\");\n                }\n                e.onload = () => {\n                  t(e);\n                }, e.onerror = (o, n, r, s, i) => {\n                  t(e, [\"Failed to load\", e, \"Error:\", i]);\n                }, S && e.setAttribute(\"nonce\", S), x.head.appendChild(e);\n              }\n            } else i({\n              messages: [\"`react-to-print` encountered a <link> tag with an empty `href` attribute. In addition to being invalid HTML, this can cause problems in many browsers, and so the <link> was not loaded. The <link> is:\", n],\n              level: \"warning\"\n            }), t(n);\n          }\n        }\n      }\n      0 === y && c(e, n);\n    }\n    function f({\n      bodyClass: e,\n      contentRef: t,\n      copyShadowRoots: o,\n      documentTitle: n,\n      fonts: r,\n      ignoreGlobalStyles: d,\n      nonce: u,\n      onAfterPrint: p,\n      onBeforePrint: f,\n      onPrintError: g,\n      pageStyle: m,\n      preserveAfterPrint: b,\n      print: y,\n      suppressErrors: v\n    }) {\n      return (0, s.useCallback)(s => {\n        function w() {\n          const l = {\n              bodyClass: e,\n              contentRef: t,\n              copyShadowRoots: o,\n              documentTitle: n,\n              fonts: r,\n              ignoreGlobalStyles: d,\n              nonce: u,\n              onAfterPrint: p,\n              onBeforePrint: f,\n              onPrintError: g,\n              pageStyle: m,\n              preserveAfterPrint: b,\n              print: y,\n              suppressErrors: v\n            },\n            a = function () {\n              const e = document.createElement(\"iframe\");\n              return e.width = `${document.documentElement.clientWidth}px`, e.height = `${document.documentElement.clientHeight}px`, e.style.position = \"absolute\", e.style.top = `-${document.documentElement.clientHeight + 100}px`, e.style.left = `-${document.documentElement.clientWidth + 100}px`, e.id = \"printWindow\", e.srcdoc = \"<!DOCTYPE html>\", e;\n            }(),\n            w = function (e, t) {\n              const {\n                  contentRef: o,\n                  fonts: n,\n                  ignoreGlobalStyles: r,\n                  suppressErrors: s\n                } = t,\n                l = function ({\n                  contentRef: e,\n                  optionalContent: t,\n                  suppressErrors: o\n                }) {\n                  return !t || t instanceof Event || (e && i({\n                    level: \"warning\",\n                    messages: ['\"react-to-print\" received a `contentRef` option and a optional-content param passed to its callback. The `contentRef` option will be ignored.']\n                  }), \"function\" != typeof t) ? e ? e.current : void i({\n                    messages: ['\"react-to-print\" did not receive a `contentRef` option or a optional-content param pass to its callback.'],\n                    suppressErrors: o\n                  }) : t();\n                }({\n                  contentRef: o,\n                  optionalContent: e,\n                  suppressErrors: s\n                });\n              if (!l) return;\n              const a = l.cloneNode(!0),\n                c = document.querySelectorAll(\"link[rel~='stylesheet'], link[as='style']\"),\n                d = a.querySelectorAll(\"img\"),\n                u = a.querySelectorAll(\"video\"),\n                p = n ? n.length : 0;\n              return {\n                contentNode: l,\n                clonedContentNode: a,\n                clonedImgNodes: d,\n                clonedVideoNodes: u,\n                numResourcesToLoad: (r ? 0 : c.length) + d.length + u.length + p,\n                originalCanvasNodes: l.querySelectorAll(\"canvas\")\n              };\n            }(s, l);\n          if (!w) return void i({\n            messages: [\"There is nothing to print\"],\n            suppressErrors: v\n          });\n          const E = function (e, t, o) {\n            const {\n                suppressErrors: n\n              } = e,\n              r = [],\n              s = [];\n            return function (l, a) {\n              r.includes(l) ? i({\n                level: \"debug\",\n                messages: [\"Tried to mark a resource that has already been handled\", l],\n                suppressErrors: n\n              }) : (a ? (i({\n                messages: ['\"react-to-print\" was unable to load a resource but will continue attempting to print the page', ...a],\n                suppressErrors: n\n              }), s.push(l)) : r.push(l), r.length + s.length === t && c(o, e));\n            };\n          }(l, w.numResourcesToLoad, a);\n          !function (e, t, o, n) {\n            e.onload = () => {\n              h(e, t, o, n);\n            }, document.body.appendChild(e);\n          }(a, E, w, l);\n        }\n        l(b, !0), f ? f().then(() => {\n          w();\n        }).catch(e => {\n          null == g || g(\"onBeforePrint\", a(e));\n        }) : w();\n      }, [e, t, o, n, r, d, u, p, f, g, m, b, y, v]);\n    }\n    return r;\n  }();\n});", "map": {"version": 3, "names": ["e", "t", "exports", "module", "require", "define", "amd", "lib", "react", "self", "o", "n", "r", "s", "d", "Object", "defineProperty", "enumerable", "get", "prototype", "hasOwnProperty", "call", "Symbol", "toStringTag", "value", "useReactToPrint", "f", "i", "level", "messages", "suppressErrors", "console", "error", "warn", "debug", "l", "document", "getElementById", "body", "<PERSON><PERSON><PERSON><PERSON>", "a", "Error", "c", "documentTitle", "onAfterPrint", "onPrintError", "preserveAfterPrint", "print", "setTimeout", "u", "contentWindow", "p", "focus", "then", "catch", "h", "contentDocument", "title", "ownerDocument", "some", "navigator", "userAgent", "vendor", "window", "opera", "match", "createTreeWalker", "Node<PERSON><PERSON><PERSON>", "SHOW_ELEMENT", "nextNode", "push", "length", "shadowRoot", "attachShadow", "mode", "innerHTML", "contentNode", "clonedContentNode", "g", "clonedImgNodes", "m", "clonedVideoNodes", "b", "numResourcesToLoad", "y", "originalCanvasNodes", "v", "bodyClass", "w", "fonts", "E", "ignoreGlobalStyles", "A", "pageStyle", "T", "nonce", "S", "P", "copyShadowRoots", "k", "onload", "x", "append<PERSON><PERSON><PERSON>", "FontFace", "for<PERSON>ach", "family", "source", "weight", "style", "add", "loaded", "createElement", "setAttribute", "head", "createTextNode", "classList", "split", "querySelectorAll", "getContext", "drawImage", "getAttribute", "Image", "onerror", "src", "preload", "readyState", "onloadeddata", "onstalled", "C", "tagName", "toLowerCase", "sheet", "cssRules", "cssText", "message", "hasAttribute", "attributes", "nodeName", "nodeValue", "contentRef", "onBeforePrint", "useCallback", "width", "documentElement", "clientWidth", "height", "clientHeight", "position", "top", "left", "id", "srcdoc", "optionalContent", "Event", "current", "cloneNode", "includes"], "sources": ["D:/My Learning Projects/ucbs_v1_08/frontend/node_modules/react-to-print/lib/index.js"], "sourcesContent": ["!function(e,t){\"object\"==typeof exports&&\"object\"==typeof module?module.exports=t(require(\"react\")):\"function\"==typeof define&&define.amd?define(\"lib\",[\"react\"],t):\"object\"==typeof exports?exports.lib=t(require(\"react\")):e.lib=t(e.react)}(\"undefined\"!=typeof self?self:this,(function(e){return function(){\"use strict\";var t={155:function(t){t.exports=e}},o={};function n(e){var r=o[e];if(void 0!==r)return r.exports;var s=o[e]={exports:{}};return t[e](s,s.exports,n),s.exports}n.d=function(e,t){for(var o in t)n.o(t,o)&&!n.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){\"undefined\"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:\"Module\"}),Object.defineProperty(e,\"__esModule\",{value:!0})};var r={};n.r(r),n.d(r,{useReactToPrint:function(){return f}});var s=n(155);function i({level:e=\"error\",messages:t,suppressErrors:o=!1}){o||(\"error\"===e?console.error(t):\"warning\"===e?console.warn(t):console.debug(t))}function l(e,t){if(t||!e){const e=document.getElementById(\"printWindow\");e&&document.body.removeChild(e)}}function a(e){return e instanceof Error?e:new Error(\"Unknown Error\")}function c(e,t){const{documentTitle:o,onAfterPrint:n,onPrintError:r,preserveAfterPrint:s,print:c,suppressErrors:d}=t;setTimeout((()=>{var t,u;if(e.contentWindow){function p(){null==n||n(),l(s)}if(e.contentWindow.focus(),c)c(e).then(p).catch((e=>{r?r(\"print\",a(e)):i({messages:[\"An error was thrown by the specified `print` function\"],suppressErrors:d})}));else{if(e.contentWindow.print){const h=null!==(u=null===(t=e.contentDocument)||void 0===t?void 0:t.title)&&void 0!==u?u:\"\",f=e.ownerDocument.title;o&&(e.ownerDocument.title=o,e.contentDocument&&(e.contentDocument.title=o)),e.contentWindow.print(),o&&(e.ownerDocument.title=f,e.contentDocument&&(e.contentDocument.title=h))}else i({messages:[\"Printing for this browser is not currently possible: the browser does not have a `print` method available for iframes.\"],suppressErrors:d});[/Android/i,/webOS/i,/iPhone/i,/iPad/i,/iPod/i,/BlackBerry/i,/Windows Phone/i].some((e=>{var t,o;return(null!==(o=null!==(t=navigator.userAgent)&&void 0!==t?t:navigator.vendor)&&void 0!==o?o:\"opera\"in window&&window.opera).match(e)}))?setTimeout(p,500):p()}}else i({messages:[\"Printing failed because the `contentWindow` of the print iframe did not load. This is possibly an error with `react-to-print`. Please file an issue: https://github.com/MatthewHerbst/react-to-print/issues/\"],suppressErrors:d})}),500)}function d(e){const t=[],o=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,null);let n=o.nextNode();for(;n;)t.push(n),n=o.nextNode();return t}function u(e,t,o){const n=d(e),r=d(t);if(n.length===r.length)for(let e=0;e<n.length;e++){const t=n[e],s=r[e],i=t.shadowRoot;if(null!==i){const e=s.attachShadow({mode:i.mode});e.innerHTML=i.innerHTML,u(i,e,o)}}else i({messages:[\"When cloning shadow root content, source and target elements have different size. `onBeforePrint` likely resolved too early.\",e,t],suppressErrors:o})}const p='\\n    @page {\\n        /* Remove browser default header (title) and footer (url) */\\n        margin: 0;\\n    }\\n    @media print {\\n        body {\\n            /* Tell browsers to print background colors */\\n            color-adjust: exact; /* Firefox. This is an older version of \"print-color-adjust\" */\\n            print-color-adjust: exact; /* Firefox/Safari */\\n            -webkit-print-color-adjust: exact; /* Chrome/Safari/Edge/Opera */\\n        }\\n    }\\n';function h(e,t,o,n){var r,s,l,d,h;const{contentNode:f,clonedContentNode:g,clonedImgNodes:m,clonedVideoNodes:b,numResourcesToLoad:y,originalCanvasNodes:v}=o,{bodyClass:w,fonts:E,ignoreGlobalStyles:A,pageStyle:T,nonce:S,suppressErrors:P,copyShadowRoots:k}=n;e.onload=null;const x=null!==(r=e.contentDocument)&&void 0!==r?r:null===(s=e.contentWindow)||void 0===s?void 0:s.document;if(x){const o=x.body.appendChild(g);k&&u(f,o,!!P),E&&((null===(l=e.contentDocument)||void 0===l?void 0:l.fonts)&&(null===(d=e.contentWindow)||void 0===d?void 0:d.FontFace)?E.forEach((o=>{const n=new FontFace(o.family,o.source,{weight:o.weight,style:o.style});e.contentDocument.fonts.add(n),n.loaded.then((()=>{t(n)})).catch((e=>{t(n,[\"Failed loading the font:\",n,\"Load error:\",a(e)])}))})):(E.forEach((e=>{t(e)})),i({messages:['\"react-to-print\" is not able to load custom fonts because the browser does not support the FontFace API but will continue attempting to print the page'],suppressErrors:P})));const n=null!=T?T:p,r=x.createElement(\"style\");S&&(r.setAttribute(\"nonce\",S),x.head.setAttribute(\"nonce\",S)),r.appendChild(x.createTextNode(n)),x.head.appendChild(r),w&&x.body.classList.add(...w.split(\" \"));const s=x.querySelectorAll(\"canvas\");for(let e=0;e<v.length;++e){const t=v[e],o=s[e];if(void 0===o){i({messages:[\"A canvas element could not be copied for printing, has it loaded? `onBeforePrint` likely resolved too early.\",t],suppressErrors:P});continue}const n=o.getContext(\"2d\");n&&n.drawImage(t,0,0)}for(let e=0;e<m.length;e++){const o=m[e],n=o.getAttribute(\"src\");if(n){const e=new Image;e.onload=()=>{t(o)},e.onerror=(e,n,r,s,i)=>{t(o,[\"Error loading <img>\",o,\"Error\",i])},e.src=n}else t(o,['Found an <img> tag with an empty \"src\" attribute. This prevents pre-loading it.',o])}for(let e=0;e<b.length;e++){const o=b[e];o.preload=\"auto\";const n=o.getAttribute(\"poster\");if(n){const e=new Image;e.onload=()=>{t(o)},e.onerror=(e,r,s,i,l)=>{t(o,[\"Error loading video poster\",n,\"for video\",o,\"Error:\",l])},e.src=n}else o.readyState>=2?t(o):o.src?(o.onloadeddata=()=>{t(o)},o.onerror=(e,n,r,s,i)=>{t(o,[\"Error loading video\",o,\"Error\",i])},o.onstalled=()=>{t(o,[\"Loading video stalled, skipping\",o])}):t(o,[\"Error loading video, `src` is empty\",o])}const c=\"select\",y=f.querySelectorAll(c),C=x.querySelectorAll(c);for(let e=0;e<y.length;e++)C[e].value=y[e].value;if(!A){const e=document.querySelectorAll(\"style, link[rel~='stylesheet'], link[as='style']\");for(let o=0,n=e.length;o<n;++o){const n=e[o];if(\"style\"===n.tagName.toLowerCase()){const e=x.createElement(n.tagName),t=n.sheet;if(t){let r=\"\";try{const e=t.cssRules.length;for(let o=0;o<e;++o)\"string\"==typeof t.cssRules[o].cssText&&(r+=`${t.cssRules[o].cssText}\\r\\n`)}catch(e){i({messages:[\"A stylesheet could not be accessed. This is likely due to the stylesheet having cross-origin imports, and many browsers block script access to cross-origin stylesheets. See https://github.com/MatthewHerbst/react-to-print/issues/429 for details. You may be able to load the sheet by both marking the stylesheet with the cross `crossorigin` attribute, and setting the `Access-Control-Allow-Origin` header on the server serving the stylesheet. Alternatively, host the stylesheet on your domain to avoid this issue entirely.\",n,`Original error: ${a(e).message}`],level:\"warning\"})}e.setAttribute(\"id\",`react-to-print-${o}`),S&&e.setAttribute(\"nonce\",S),e.appendChild(x.createTextNode(r)),x.head.appendChild(e)}}else if(n.getAttribute(\"href\"))if(n.hasAttribute(\"disabled\"))i({messages:[\"`react-to-print` encountered a <link> tag with a `disabled` attribute and will ignore it. Note that the `disabled` attribute is deprecated, and some browsers ignore it. You should stop using it. https://developer.mozilla.org/en-US/docs/Web/HTML/Element/link#attr-disabled. The <link> is:\",n],level:\"warning\"}),t(n);else{const e=x.createElement(n.tagName);for(let t=0,o=n.attributes.length;t<o;++t){const o=n.attributes[t];o&&e.setAttribute(o.nodeName,null!==(h=o.nodeValue)&&void 0!==h?h:\"\")}e.onload=()=>{t(e)},e.onerror=(o,n,r,s,i)=>{t(e,[\"Failed to load\",e,\"Error:\",i])},S&&e.setAttribute(\"nonce\",S),x.head.appendChild(e)}else i({messages:[\"`react-to-print` encountered a <link> tag with an empty `href` attribute. In addition to being invalid HTML, this can cause problems in many browsers, and so the <link> was not loaded. The <link> is:\",n],level:\"warning\"}),t(n)}}}0===y&&c(e,n)}function f({bodyClass:e,contentRef:t,copyShadowRoots:o,documentTitle:n,fonts:r,ignoreGlobalStyles:d,nonce:u,onAfterPrint:p,onBeforePrint:f,onPrintError:g,pageStyle:m,preserveAfterPrint:b,print:y,suppressErrors:v}){return(0,s.useCallback)((s=>{function w(){const l={bodyClass:e,contentRef:t,copyShadowRoots:o,documentTitle:n,fonts:r,ignoreGlobalStyles:d,nonce:u,onAfterPrint:p,onBeforePrint:f,onPrintError:g,pageStyle:m,preserveAfterPrint:b,print:y,suppressErrors:v},a=function(){const e=document.createElement(\"iframe\");return e.width=`${document.documentElement.clientWidth}px`,e.height=`${document.documentElement.clientHeight}px`,e.style.position=\"absolute\",e.style.top=`-${document.documentElement.clientHeight+100}px`,e.style.left=`-${document.documentElement.clientWidth+100}px`,e.id=\"printWindow\",e.srcdoc=\"<!DOCTYPE html>\",e}(),w=function(e,t){const{contentRef:o,fonts:n,ignoreGlobalStyles:r,suppressErrors:s}=t,l=function({contentRef:e,optionalContent:t,suppressErrors:o}){return!t||t instanceof Event||(e&&i({level:\"warning\",messages:['\"react-to-print\" received a `contentRef` option and a optional-content param passed to its callback. The `contentRef` option will be ignored.']}),\"function\"!=typeof t)?e?e.current:void i({messages:['\"react-to-print\" did not receive a `contentRef` option or a optional-content param pass to its callback.'],suppressErrors:o}):t()}({contentRef:o,optionalContent:e,suppressErrors:s});if(!l)return;const a=l.cloneNode(!0),c=document.querySelectorAll(\"link[rel~='stylesheet'], link[as='style']\"),d=a.querySelectorAll(\"img\"),u=a.querySelectorAll(\"video\"),p=n?n.length:0;return{contentNode:l,clonedContentNode:a,clonedImgNodes:d,clonedVideoNodes:u,numResourcesToLoad:(r?0:c.length)+d.length+u.length+p,originalCanvasNodes:l.querySelectorAll(\"canvas\")}}(s,l);if(!w)return void i({messages:[\"There is nothing to print\"],suppressErrors:v});const E=function(e,t,o){const{suppressErrors:n}=e,r=[],s=[];return function(l,a){r.includes(l)?i({level:\"debug\",messages:[\"Tried to mark a resource that has already been handled\",l],suppressErrors:n}):(a?(i({messages:['\"react-to-print\" was unable to load a resource but will continue attempting to print the page',...a],suppressErrors:n}),s.push(l)):r.push(l),r.length+s.length===t&&c(o,e))}}(l,w.numResourcesToLoad,a);!function(e,t,o,n){e.onload=()=>{h(e,t,o,n)},document.body.appendChild(e)}(a,E,w,l)}l(b,!0),f?f().then((()=>{w()})).catch((e=>{null==g||g(\"onBeforePrint\",a(e))})):w()}),[e,t,o,n,r,d,u,p,f,g,m,b,y,v])}return r}()}));"], "mappings": "AAAA,CAAC,UAASA,CAAC,EAACC,CAAC,EAAC;EAAC,QAAQ,IAAE,OAAOC,OAAO,IAAE,QAAQ,IAAE,OAAOC,MAAM,GAACA,MAAM,CAACD,OAAO,GAACD,CAAC,CAACG,OAAO,CAAC,OAAO,CAAC,CAAC,GAAC,UAAU,IAAE,OAAOC,MAAM,IAAEA,MAAM,CAACC,GAAG,GAACD,MAAM,CAAC,KAAK,EAAC,CAAC,OAAO,CAAC,EAACJ,CAAC,CAAC,GAAC,QAAQ,IAAE,OAAOC,OAAO,GAACA,OAAO,CAACK,GAAG,GAACN,CAAC,CAACG,OAAO,CAAC,OAAO,CAAC,CAAC,GAACJ,CAAC,CAACO,GAAG,GAACN,CAAC,CAACD,CAAC,CAACQ,KAAK,CAAC;AAAA,CAAC,CAAC,WAAW,IAAE,OAAOC,IAAI,GAACA,IAAI,GAAC,IAAI,EAAE,UAAST,CAAC,EAAC;EAAC,OAAO,YAAU;IAAC,YAAY;;IAAC,IAAIC,CAAC,GAAC;QAAC,GAAG,EAAC,UAASA,CAAC,EAAC;UAACA,CAAC,CAACC,OAAO,GAACF,CAAC;QAAA;MAAC,CAAC;MAACU,CAAC,GAAC,CAAC,CAAC;IAAC,SAASC,CAACA,CAACX,CAAC,EAAC;MAAC,IAAIY,CAAC,GAACF,CAAC,CAACV,CAAC,CAAC;MAAC,IAAG,KAAK,CAAC,KAAGY,CAAC,EAAC,OAAOA,CAAC,CAACV,OAAO;MAAC,IAAIW,CAAC,GAACH,CAAC,CAACV,CAAC,CAAC,GAAC;QAACE,OAAO,EAAC,CAAC;MAAC,CAAC;MAAC,OAAOD,CAAC,CAACD,CAAC,CAAC,CAACa,CAAC,EAACA,CAAC,CAACX,OAAO,EAACS,CAAC,CAAC,EAACE,CAAC,CAACX,OAAO;IAAA;IAACS,CAAC,CAACG,CAAC,GAAC,UAASd,CAAC,EAACC,CAAC,EAAC;MAAC,KAAI,IAAIS,CAAC,IAAIT,CAAC,EAACU,CAAC,CAACD,CAAC,CAACT,CAAC,EAACS,CAAC,CAAC,IAAE,CAACC,CAAC,CAACD,CAAC,CAACV,CAAC,EAACU,CAAC,CAAC,IAAEK,MAAM,CAACC,cAAc,CAAChB,CAAC,EAACU,CAAC,EAAC;QAACO,UAAU,EAAC,CAAC,CAAC;QAACC,GAAG,EAACjB,CAAC,CAACS,CAAC;MAAC,CAAC,CAAC;IAAA,CAAC,EAACC,CAAC,CAACD,CAAC,GAAC,UAASV,CAAC,EAACC,CAAC,EAAC;MAAC,OAAOc,MAAM,CAACI,SAAS,CAACC,cAAc,CAACC,IAAI,CAACrB,CAAC,EAACC,CAAC,CAAC;IAAA,CAAC,EAACU,CAAC,CAACC,CAAC,GAAC,UAASZ,CAAC,EAAC;MAAC,WAAW,IAAE,OAAOsB,MAAM,IAAEA,MAAM,CAACC,WAAW,IAAER,MAAM,CAACC,cAAc,CAAChB,CAAC,EAACsB,MAAM,CAACC,WAAW,EAAC;QAACC,KAAK,EAAC;MAAQ,CAAC,CAAC,EAACT,MAAM,CAACC,cAAc,CAAChB,CAAC,EAAC,YAAY,EAAC;QAACwB,KAAK,EAAC,CAAC;MAAC,CAAC,CAAC;IAAA,CAAC;IAAC,IAAIZ,CAAC,GAAC,CAAC,CAAC;IAACD,CAAC,CAACC,CAAC,CAACA,CAAC,CAAC,EAACD,CAAC,CAACG,CAAC,CAACF,CAAC,EAAC;MAACa,eAAe,EAAC,SAAAA,CAAA,EAAU;QAAC,OAAOC,CAAC;MAAA;IAAC,CAAC,CAAC;IAAC,IAAIb,CAAC,GAACF,CAAC,CAAC,GAAG,CAAC;IAAC,SAASgB,CAACA,CAAC;MAACC,KAAK,EAAC5B,CAAC,GAAC,OAAO;MAAC6B,QAAQ,EAAC5B,CAAC;MAAC6B,cAAc,EAACpB,CAAC,GAAC,CAAC;IAAC,CAAC,EAAC;MAACA,CAAC,KAAG,OAAO,KAAGV,CAAC,GAAC+B,OAAO,CAACC,KAAK,CAAC/B,CAAC,CAAC,GAAC,SAAS,KAAGD,CAAC,GAAC+B,OAAO,CAACE,IAAI,CAAChC,CAAC,CAAC,GAAC8B,OAAO,CAACG,KAAK,CAACjC,CAAC,CAAC,CAAC;IAAA;IAAC,SAASkC,CAACA,CAACnC,CAAC,EAACC,CAAC,EAAC;MAAC,IAAGA,CAAC,IAAE,CAACD,CAAC,EAAC;QAAC,MAAMA,CAAC,GAACoC,QAAQ,CAACC,cAAc,CAAC,aAAa,CAAC;QAACrC,CAAC,IAAEoC,QAAQ,CAACE,IAAI,CAACC,WAAW,CAACvC,CAAC,CAAC;MAAA;IAAC;IAAC,SAASwC,CAACA,CAACxC,CAAC,EAAC;MAAC,OAAOA,CAAC,YAAYyC,KAAK,GAACzC,CAAC,GAAC,IAAIyC,KAAK,CAAC,eAAe,CAAC;IAAA;IAAC,SAASC,CAACA,CAAC1C,CAAC,EAACC,CAAC,EAAC;MAAC,MAAK;QAAC0C,aAAa,EAACjC,CAAC;QAACkC,YAAY,EAACjC,CAAC;QAACkC,YAAY,EAACjC,CAAC;QAACkC,kBAAkB,EAACjC,CAAC;QAACkC,KAAK,EAACL,CAAC;QAACZ,cAAc,EAAChB;MAAC,CAAC,GAACb,CAAC;MAAC+C,UAAU,CAAE,MAAI;QAAC,IAAI/C,CAAC,EAACgD,CAAC;QAAC,IAAGjD,CAAC,CAACkD,aAAa,EAAC;UAAC,SAASC,CAACA,CAAA,EAAE;YAAC,IAAI,IAAExC,CAAC,IAAEA,CAAC,CAAC,CAAC,EAACwB,CAAC,CAACtB,CAAC,CAAC;UAAA;UAAC,IAAGb,CAAC,CAACkD,aAAa,CAACE,KAAK,CAAC,CAAC,EAACV,CAAC,EAACA,CAAC,CAAC1C,CAAC,CAAC,CAACqD,IAAI,CAACF,CAAC,CAAC,CAACG,KAAK,CAAEtD,CAAC,IAAE;YAACY,CAAC,GAACA,CAAC,CAAC,OAAO,EAAC4B,CAAC,CAACxC,CAAC,CAAC,CAAC,GAAC2B,CAAC,CAAC;cAACE,QAAQ,EAAC,CAAC,uDAAuD,CAAC;cAACC,cAAc,EAAChB;YAAC,CAAC,CAAC;UAAA,CAAE,CAAC,CAAC,KAAI;YAAC,IAAGd,CAAC,CAACkD,aAAa,CAACH,KAAK,EAAC;cAAC,MAAMQ,CAAC,GAAC,IAAI,MAAIN,CAAC,GAAC,IAAI,MAAIhD,CAAC,GAACD,CAAC,CAACwD,eAAe,CAAC,IAAE,KAAK,CAAC,KAAGvD,CAAC,GAAC,KAAK,CAAC,GAACA,CAAC,CAACwD,KAAK,CAAC,IAAE,KAAK,CAAC,KAAGR,CAAC,GAACA,CAAC,GAAC,EAAE;gBAACvB,CAAC,GAAC1B,CAAC,CAAC0D,aAAa,CAACD,KAAK;cAAC/C,CAAC,KAAGV,CAAC,CAAC0D,aAAa,CAACD,KAAK,GAAC/C,CAAC,EAACV,CAAC,CAACwD,eAAe,KAAGxD,CAAC,CAACwD,eAAe,CAACC,KAAK,GAAC/C,CAAC,CAAC,CAAC,EAACV,CAAC,CAACkD,aAAa,CAACH,KAAK,CAAC,CAAC,EAACrC,CAAC,KAAGV,CAAC,CAAC0D,aAAa,CAACD,KAAK,GAAC/B,CAAC,EAAC1B,CAAC,CAACwD,eAAe,KAAGxD,CAAC,CAACwD,eAAe,CAACC,KAAK,GAACF,CAAC,CAAC,CAAC;YAAA,CAAC,MAAK5B,CAAC,CAAC;cAACE,QAAQ,EAAC,CAAC,wHAAwH,CAAC;cAACC,cAAc,EAAChB;YAAC,CAAC,CAAC;YAAC,CAAC,UAAU,EAAC,QAAQ,EAAC,SAAS,EAAC,OAAO,EAAC,OAAO,EAAC,aAAa,EAAC,gBAAgB,CAAC,CAAC6C,IAAI,CAAE3D,CAAC,IAAE;cAAC,IAAIC,CAAC,EAACS,CAAC;cAAC,OAAM,CAAC,IAAI,MAAIA,CAAC,GAAC,IAAI,MAAIT,CAAC,GAAC2D,SAAS,CAACC,SAAS,CAAC,IAAE,KAAK,CAAC,KAAG5D,CAAC,GAACA,CAAC,GAAC2D,SAAS,CAACE,MAAM,CAAC,IAAE,KAAK,CAAC,KAAGpD,CAAC,GAACA,CAAC,GAAC,OAAO,IAAGqD,MAAM,IAAEA,MAAM,CAACC,KAAK,EAAEC,KAAK,CAACjE,CAAC,CAAC;YAAA,CAAE,CAAC,GAACgD,UAAU,CAACG,CAAC,EAAC,GAAG,CAAC,GAACA,CAAC,CAAC,CAAC;UAAA;QAAC,CAAC,MAAKxB,CAAC,CAAC;UAACE,QAAQ,EAAC,CAAC,8MAA8M,CAAC;UAACC,cAAc,EAAChB;QAAC,CAAC,CAAC;MAAA,CAAC,EAAE,GAAG,CAAC;IAAA;IAAC,SAASA,CAACA,CAACd,CAAC,EAAC;MAAC,MAAMC,CAAC,GAAC,EAAE;QAACS,CAAC,GAAC0B,QAAQ,CAAC8B,gBAAgB,CAAClE,CAAC,EAACmE,UAAU,CAACC,YAAY,EAAC,IAAI,CAAC;MAAC,IAAIzD,CAAC,GAACD,CAAC,CAAC2D,QAAQ,CAAC,CAAC;MAAC,OAAK1D,CAAC,GAAEV,CAAC,CAACqE,IAAI,CAAC3D,CAAC,CAAC,EAACA,CAAC,GAACD,CAAC,CAAC2D,QAAQ,CAAC,CAAC;MAAC,OAAOpE,CAAC;IAAA;IAAC,SAASgD,CAACA,CAACjD,CAAC,EAACC,CAAC,EAACS,CAAC,EAAC;MAAC,MAAMC,CAAC,GAACG,CAAC,CAACd,CAAC,CAAC;QAACY,CAAC,GAACE,CAAC,CAACb,CAAC,CAAC;MAAC,IAAGU,CAAC,CAAC4D,MAAM,KAAG3D,CAAC,CAAC2D,MAAM,EAAC,KAAI,IAAIvE,CAAC,GAAC,CAAC,EAACA,CAAC,GAACW,CAAC,CAAC4D,MAAM,EAACvE,CAAC,EAAE,EAAC;QAAC,MAAMC,CAAC,GAACU,CAAC,CAACX,CAAC,CAAC;UAACa,CAAC,GAACD,CAAC,CAACZ,CAAC,CAAC;UAAC2B,CAAC,GAAC1B,CAAC,CAACuE,UAAU;QAAC,IAAG,IAAI,KAAG7C,CAAC,EAAC;UAAC,MAAM3B,CAAC,GAACa,CAAC,CAAC4D,YAAY,CAAC;YAACC,IAAI,EAAC/C,CAAC,CAAC+C;UAAI,CAAC,CAAC;UAAC1E,CAAC,CAAC2E,SAAS,GAAChD,CAAC,CAACgD,SAAS,EAAC1B,CAAC,CAACtB,CAAC,EAAC3B,CAAC,EAACU,CAAC,CAAC;QAAA;MAAC,CAAC,MAAKiB,CAAC,CAAC;QAACE,QAAQ,EAAC,CAAC,8HAA8H,EAAC7B,CAAC,EAACC,CAAC,CAAC;QAAC6B,cAAc,EAACpB;MAAC,CAAC,CAAC;IAAA;IAAC,MAAMyC,CAAC,GAAC,kdAAkd;IAAC,SAASI,CAACA,CAACvD,CAAC,EAACC,CAAC,EAACS,CAAC,EAACC,CAAC,EAAC;MAAC,IAAIC,CAAC,EAACC,CAAC,EAACsB,CAAC,EAACrB,CAAC,EAACyC,CAAC;MAAC,MAAK;UAACqB,WAAW,EAAClD,CAAC;UAACmD,iBAAiB,EAACC,CAAC;UAACC,cAAc,EAACC,CAAC;UAACC,gBAAgB,EAACC,CAAC;UAACC,kBAAkB,EAACC,CAAC;UAACC,mBAAmB,EAACC;QAAC,CAAC,GAAC5E,CAAC;QAAC;UAAC6E,SAAS,EAACC,CAAC;UAACC,KAAK,EAACC,CAAC;UAACC,kBAAkB,EAACC,CAAC;UAACC,SAAS,EAACC,CAAC;UAACC,KAAK,EAACC,CAAC;UAAClE,cAAc,EAACmE,CAAC;UAACC,eAAe,EAACC;QAAC,CAAC,GAACxF,CAAC;MAACX,CAAC,CAACoG,MAAM,GAAC,IAAI;MAAC,MAAMC,CAAC,GAAC,IAAI,MAAIzF,CAAC,GAACZ,CAAC,CAACwD,eAAe,CAAC,IAAE,KAAK,CAAC,KAAG5C,CAAC,GAACA,CAAC,GAAC,IAAI,MAAIC,CAAC,GAACb,CAAC,CAACkD,aAAa,CAAC,IAAE,KAAK,CAAC,KAAGrC,CAAC,GAAC,KAAK,CAAC,GAACA,CAAC,CAACuB,QAAQ;MAAC,IAAGiE,CAAC,EAAC;QAAC,MAAM3F,CAAC,GAAC2F,CAAC,CAAC/D,IAAI,CAACgE,WAAW,CAACxB,CAAC,CAAC;QAACqB,CAAC,IAAElD,CAAC,CAACvB,CAAC,EAAChB,CAAC,EAAC,CAAC,CAACuF,CAAC,CAAC,EAACP,CAAC,KAAG,CAAC,IAAI,MAAIvD,CAAC,GAACnC,CAAC,CAACwD,eAAe,CAAC,IAAE,KAAK,CAAC,KAAGrB,CAAC,GAAC,KAAK,CAAC,GAACA,CAAC,CAACsD,KAAK,MAAI,IAAI,MAAI3E,CAAC,GAACd,CAAC,CAACkD,aAAa,CAAC,IAAE,KAAK,CAAC,KAAGpC,CAAC,GAAC,KAAK,CAAC,GAACA,CAAC,CAACyF,QAAQ,CAAC,GAACb,CAAC,CAACc,OAAO,CAAE9F,CAAC,IAAE;UAAC,MAAMC,CAAC,GAAC,IAAI4F,QAAQ,CAAC7F,CAAC,CAAC+F,MAAM,EAAC/F,CAAC,CAACgG,MAAM,EAAC;YAACC,MAAM,EAACjG,CAAC,CAACiG,MAAM;YAACC,KAAK,EAAClG,CAAC,CAACkG;UAAK,CAAC,CAAC;UAAC5G,CAAC,CAACwD,eAAe,CAACiC,KAAK,CAACoB,GAAG,CAAClG,CAAC,CAAC,EAACA,CAAC,CAACmG,MAAM,CAACzD,IAAI,CAAE,MAAI;YAACpD,CAAC,CAACU,CAAC,CAAC;UAAA,CAAE,CAAC,CAAC2C,KAAK,CAAEtD,CAAC,IAAE;YAACC,CAAC,CAACU,CAAC,EAAC,CAAC,0BAA0B,EAACA,CAAC,EAAC,aAAa,EAAC6B,CAAC,CAACxC,CAAC,CAAC,CAAC,CAAC;UAAA,CAAE,CAAC;QAAA,CAAE,CAAC,IAAE0F,CAAC,CAACc,OAAO,CAAExG,CAAC,IAAE;UAACC,CAAC,CAACD,CAAC,CAAC;QAAA,CAAE,CAAC,EAAC2B,CAAC,CAAC;UAACE,QAAQ,EAAC,CAAC,wJAAwJ,CAAC;UAACC,cAAc,EAACmE;QAAC,CAAC,CAAC,CAAC,CAAC;QAAC,MAAMtF,CAAC,GAAC,IAAI,IAAEmF,CAAC,GAACA,CAAC,GAAC3C,CAAC;UAACvC,CAAC,GAACyF,CAAC,CAACU,aAAa,CAAC,OAAO,CAAC;QAACf,CAAC,KAAGpF,CAAC,CAACoG,YAAY,CAAC,OAAO,EAAChB,CAAC,CAAC,EAACK,CAAC,CAACY,IAAI,CAACD,YAAY,CAAC,OAAO,EAAChB,CAAC,CAAC,CAAC,EAACpF,CAAC,CAAC0F,WAAW,CAACD,CAAC,CAACa,cAAc,CAACvG,CAAC,CAAC,CAAC,EAAC0F,CAAC,CAACY,IAAI,CAACX,WAAW,CAAC1F,CAAC,CAAC,EAAC4E,CAAC,IAAEa,CAAC,CAAC/D,IAAI,CAAC6E,SAAS,CAACN,GAAG,CAAC,GAAGrB,CAAC,CAAC4B,KAAK,CAAC,GAAG,CAAC,CAAC;QAAC,MAAMvG,CAAC,GAACwF,CAAC,CAACgB,gBAAgB,CAAC,QAAQ,CAAC;QAAC,KAAI,IAAIrH,CAAC,GAAC,CAAC,EAACA,CAAC,GAACsF,CAAC,CAACf,MAAM,EAAC,EAAEvE,CAAC,EAAC;UAAC,MAAMC,CAAC,GAACqF,CAAC,CAACtF,CAAC,CAAC;YAACU,CAAC,GAACG,CAAC,CAACb,CAAC,CAAC;UAAC,IAAG,KAAK,CAAC,KAAGU,CAAC,EAAC;YAACiB,CAAC,CAAC;cAACE,QAAQ,EAAC,CAAC,8GAA8G,EAAC5B,CAAC,CAAC;cAAC6B,cAAc,EAACmE;YAAC,CAAC,CAAC;YAAC;UAAQ;UAAC,MAAMtF,CAAC,GAACD,CAAC,CAAC4G,UAAU,CAAC,IAAI,CAAC;UAAC3G,CAAC,IAAEA,CAAC,CAAC4G,SAAS,CAACtH,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC;QAAA;QAAC,KAAI,IAAID,CAAC,GAAC,CAAC,EAACA,CAAC,GAACgF,CAAC,CAACT,MAAM,EAACvE,CAAC,EAAE,EAAC;UAAC,MAAMU,CAAC,GAACsE,CAAC,CAAChF,CAAC,CAAC;YAACW,CAAC,GAACD,CAAC,CAAC8G,YAAY,CAAC,KAAK,CAAC;UAAC,IAAG7G,CAAC,EAAC;YAAC,MAAMX,CAAC,GAAC,IAAIyH,KAAK,CAAD,CAAC;YAACzH,CAAC,CAACoG,MAAM,GAAC,MAAI;cAACnG,CAAC,CAACS,CAAC,CAAC;YAAA,CAAC,EAACV,CAAC,CAAC0H,OAAO,GAAC,CAAC1H,CAAC,EAACW,CAAC,EAACC,CAAC,EAACC,CAAC,EAACc,CAAC,KAAG;cAAC1B,CAAC,CAACS,CAAC,EAAC,CAAC,qBAAqB,EAACA,CAAC,EAAC,OAAO,EAACiB,CAAC,CAAC,CAAC;YAAA,CAAC,EAAC3B,CAAC,CAAC2H,GAAG,GAAChH,CAAC;UAAA,CAAC,MAAKV,CAAC,CAACS,CAAC,EAAC,CAAC,iFAAiF,EAACA,CAAC,CAAC,CAAC;QAAA;QAAC,KAAI,IAAIV,CAAC,GAAC,CAAC,EAACA,CAAC,GAACkF,CAAC,CAACX,MAAM,EAACvE,CAAC,EAAE,EAAC;UAAC,MAAMU,CAAC,GAACwE,CAAC,CAAClF,CAAC,CAAC;UAACU,CAAC,CAACkH,OAAO,GAAC,MAAM;UAAC,MAAMjH,CAAC,GAACD,CAAC,CAAC8G,YAAY,CAAC,QAAQ,CAAC;UAAC,IAAG7G,CAAC,EAAC;YAAC,MAAMX,CAAC,GAAC,IAAIyH,KAAK,CAAD,CAAC;YAACzH,CAAC,CAACoG,MAAM,GAAC,MAAI;cAACnG,CAAC,CAACS,CAAC,CAAC;YAAA,CAAC,EAACV,CAAC,CAAC0H,OAAO,GAAC,CAAC1H,CAAC,EAACY,CAAC,EAACC,CAAC,EAACc,CAAC,EAACQ,CAAC,KAAG;cAAClC,CAAC,CAACS,CAAC,EAAC,CAAC,4BAA4B,EAACC,CAAC,EAAC,WAAW,EAACD,CAAC,EAAC,QAAQ,EAACyB,CAAC,CAAC,CAAC;YAAA,CAAC,EAACnC,CAAC,CAAC2H,GAAG,GAAChH,CAAC;UAAA,CAAC,MAAKD,CAAC,CAACmH,UAAU,IAAE,CAAC,GAAC5H,CAAC,CAACS,CAAC,CAAC,GAACA,CAAC,CAACiH,GAAG,IAAEjH,CAAC,CAACoH,YAAY,GAAC,MAAI;YAAC7H,CAAC,CAACS,CAAC,CAAC;UAAA,CAAC,EAACA,CAAC,CAACgH,OAAO,GAAC,CAAC1H,CAAC,EAACW,CAAC,EAACC,CAAC,EAACC,CAAC,EAACc,CAAC,KAAG;YAAC1B,CAAC,CAACS,CAAC,EAAC,CAAC,qBAAqB,EAACA,CAAC,EAAC,OAAO,EAACiB,CAAC,CAAC,CAAC;UAAA,CAAC,EAACjB,CAAC,CAACqH,SAAS,GAAC,MAAI;YAAC9H,CAAC,CAACS,CAAC,EAAC,CAAC,iCAAiC,EAACA,CAAC,CAAC,CAAC;UAAA,CAAC,IAAET,CAAC,CAACS,CAAC,EAAC,CAAC,qCAAqC,EAACA,CAAC,CAAC,CAAC;QAAA;QAAC,MAAMgC,CAAC,GAAC,QAAQ;UAAC0C,CAAC,GAAC1D,CAAC,CAAC2F,gBAAgB,CAAC3E,CAAC,CAAC;UAACsF,CAAC,GAAC3B,CAAC,CAACgB,gBAAgB,CAAC3E,CAAC,CAAC;QAAC,KAAI,IAAI1C,CAAC,GAAC,CAAC,EAACA,CAAC,GAACoF,CAAC,CAACb,MAAM,EAACvE,CAAC,EAAE,EAACgI,CAAC,CAAChI,CAAC,CAAC,CAACwB,KAAK,GAAC4D,CAAC,CAACpF,CAAC,CAAC,CAACwB,KAAK;QAAC,IAAG,CAACoE,CAAC,EAAC;UAAC,MAAM5F,CAAC,GAACoC,QAAQ,CAACiF,gBAAgB,CAAC,kDAAkD,CAAC;UAAC,KAAI,IAAI3G,CAAC,GAAC,CAAC,EAACC,CAAC,GAACX,CAAC,CAACuE,MAAM,EAAC7D,CAAC,GAACC,CAAC,EAAC,EAAED,CAAC,EAAC;YAAC,MAAMC,CAAC,GAACX,CAAC,CAACU,CAAC,CAAC;YAAC,IAAG,OAAO,KAAGC,CAAC,CAACsH,OAAO,CAACC,WAAW,CAAC,CAAC,EAAC;cAAC,MAAMlI,CAAC,GAACqG,CAAC,CAACU,aAAa,CAACpG,CAAC,CAACsH,OAAO,CAAC;gBAAChI,CAAC,GAACU,CAAC,CAACwH,KAAK;cAAC,IAAGlI,CAAC,EAAC;gBAAC,IAAIW,CAAC,GAAC,EAAE;gBAAC,IAAG;kBAAC,MAAMZ,CAAC,GAACC,CAAC,CAACmI,QAAQ,CAAC7D,MAAM;kBAAC,KAAI,IAAI7D,CAAC,GAAC,CAAC,EAACA,CAAC,GAACV,CAAC,EAAC,EAAEU,CAAC,EAAC,QAAQ,IAAE,OAAOT,CAAC,CAACmI,QAAQ,CAAC1H,CAAC,CAAC,CAAC2H,OAAO,KAAGzH,CAAC,IAAE,GAAGX,CAAC,CAACmI,QAAQ,CAAC1H,CAAC,CAAC,CAAC2H,OAAO,MAAM,CAAC;gBAAA,CAAC,QAAMrI,CAAC,EAAC;kBAAC2B,CAAC,CAAC;oBAACE,QAAQ,EAAC,CAAC,0gBAA0gB,EAAClB,CAAC,EAAC,mBAAmB6B,CAAC,CAACxC,CAAC,CAAC,CAACsI,OAAO,EAAE,CAAC;oBAAC1G,KAAK,EAAC;kBAAS,CAAC,CAAC;gBAAA;gBAAC5B,CAAC,CAACgH,YAAY,CAAC,IAAI,EAAC,kBAAkBtG,CAAC,EAAE,CAAC,EAACsF,CAAC,IAAEhG,CAAC,CAACgH,YAAY,CAAC,OAAO,EAAChB,CAAC,CAAC,EAAChG,CAAC,CAACsG,WAAW,CAACD,CAAC,CAACa,cAAc,CAACtG,CAAC,CAAC,CAAC,EAACyF,CAAC,CAACY,IAAI,CAACX,WAAW,CAACtG,CAAC,CAAC;cAAA;YAAC,CAAC,MAAK,IAAGW,CAAC,CAAC6G,YAAY,CAAC,MAAM,CAAC;cAAC,IAAG7G,CAAC,CAAC4H,YAAY,CAAC,UAAU,CAAC,EAAC5G,CAAC,CAAC;gBAACE,QAAQ,EAAC,CAAC,iSAAiS,EAAClB,CAAC,CAAC;gBAACiB,KAAK,EAAC;cAAS,CAAC,CAAC,EAAC3B,CAAC,CAACU,CAAC,CAAC,CAAC,KAAI;gBAAC,MAAMX,CAAC,GAACqG,CAAC,CAACU,aAAa,CAACpG,CAAC,CAACsH,OAAO,CAAC;gBAAC,KAAI,IAAIhI,CAAC,GAAC,CAAC,EAACS,CAAC,GAACC,CAAC,CAAC6H,UAAU,CAACjE,MAAM,EAACtE,CAAC,GAACS,CAAC,EAAC,EAAET,CAAC,EAAC;kBAAC,MAAMS,CAAC,GAACC,CAAC,CAAC6H,UAAU,CAACvI,CAAC,CAAC;kBAACS,CAAC,IAAEV,CAAC,CAACgH,YAAY,CAACtG,CAAC,CAAC+H,QAAQ,EAAC,IAAI,MAAIlF,CAAC,GAAC7C,CAAC,CAACgI,SAAS,CAAC,IAAE,KAAK,CAAC,KAAGnF,CAAC,GAACA,CAAC,GAAC,EAAE,CAAC;gBAAA;gBAACvD,CAAC,CAACoG,MAAM,GAAC,MAAI;kBAACnG,CAAC,CAACD,CAAC,CAAC;gBAAA,CAAC,EAACA,CAAC,CAAC0H,OAAO,GAAC,CAAChH,CAAC,EAACC,CAAC,EAACC,CAAC,EAACC,CAAC,EAACc,CAAC,KAAG;kBAAC1B,CAAC,CAACD,CAAC,EAAC,CAAC,gBAAgB,EAACA,CAAC,EAAC,QAAQ,EAAC2B,CAAC,CAAC,CAAC;gBAAA,CAAC,EAACqE,CAAC,IAAEhG,CAAC,CAACgH,YAAY,CAAC,OAAO,EAAChB,CAAC,CAAC,EAACK,CAAC,CAACY,IAAI,CAACX,WAAW,CAACtG,CAAC,CAAC;cAAA;YAAC,OAAK2B,CAAC,CAAC;cAACE,QAAQ,EAAC,CAAC,yMAAyM,EAAClB,CAAC,CAAC;cAACiB,KAAK,EAAC;YAAS,CAAC,CAAC,EAAC3B,CAAC,CAACU,CAAC,CAAC;UAAA;QAAC;MAAC;MAAC,CAAC,KAAGyE,CAAC,IAAE1C,CAAC,CAAC1C,CAAC,EAACW,CAAC,CAAC;IAAA;IAAC,SAASe,CAACA,CAAC;MAAC6D,SAAS,EAACvF,CAAC;MAAC2I,UAAU,EAAC1I,CAAC;MAACiG,eAAe,EAACxF,CAAC;MAACiC,aAAa,EAAChC,CAAC;MAAC8E,KAAK,EAAC7E,CAAC;MAAC+E,kBAAkB,EAAC7E,CAAC;MAACiF,KAAK,EAAC9C,CAAC;MAACL,YAAY,EAACO,CAAC;MAACyF,aAAa,EAAClH,CAAC;MAACmB,YAAY,EAACiC,CAAC;MAACe,SAAS,EAACb,CAAC;MAAClC,kBAAkB,EAACoC,CAAC;MAACnC,KAAK,EAACqC,CAAC;MAACtD,cAAc,EAACwD;IAAC,CAAC,EAAC;MAAC,OAAM,CAAC,CAAC,EAACzE,CAAC,CAACgI,WAAW,EAAGhI,CAAC,IAAE;QAAC,SAAS2E,CAACA,CAAA,EAAE;UAAC,MAAMrD,CAAC,GAAC;cAACoD,SAAS,EAACvF,CAAC;cAAC2I,UAAU,EAAC1I,CAAC;cAACiG,eAAe,EAACxF,CAAC;cAACiC,aAAa,EAAChC,CAAC;cAAC8E,KAAK,EAAC7E,CAAC;cAAC+E,kBAAkB,EAAC7E,CAAC;cAACiF,KAAK,EAAC9C,CAAC;cAACL,YAAY,EAACO,CAAC;cAACyF,aAAa,EAAClH,CAAC;cAACmB,YAAY,EAACiC,CAAC;cAACe,SAAS,EAACb,CAAC;cAAClC,kBAAkB,EAACoC,CAAC;cAACnC,KAAK,EAACqC,CAAC;cAACtD,cAAc,EAACwD;YAAC,CAAC;YAAC9C,CAAC,GAAC,YAAU;cAAC,MAAMxC,CAAC,GAACoC,QAAQ,CAAC2E,aAAa,CAAC,QAAQ,CAAC;cAAC,OAAO/G,CAAC,CAAC8I,KAAK,GAAC,GAAG1G,QAAQ,CAAC2G,eAAe,CAACC,WAAW,IAAI,EAAChJ,CAAC,CAACiJ,MAAM,GAAC,GAAG7G,QAAQ,CAAC2G,eAAe,CAACG,YAAY,IAAI,EAAClJ,CAAC,CAAC4G,KAAK,CAACuC,QAAQ,GAAC,UAAU,EAACnJ,CAAC,CAAC4G,KAAK,CAACwC,GAAG,GAAC,IAAIhH,QAAQ,CAAC2G,eAAe,CAACG,YAAY,GAAC,GAAG,IAAI,EAAClJ,CAAC,CAAC4G,KAAK,CAACyC,IAAI,GAAC,IAAIjH,QAAQ,CAAC2G,eAAe,CAACC,WAAW,GAAC,GAAG,IAAI,EAAChJ,CAAC,CAACsJ,EAAE,GAAC,aAAa,EAACtJ,CAAC,CAACuJ,MAAM,GAAC,iBAAiB,EAACvJ,CAAC;YAAA,CAAC,CAAC,CAAC;YAACwF,CAAC,GAAC,UAASxF,CAAC,EAACC,CAAC,EAAC;cAAC,MAAK;kBAAC0I,UAAU,EAACjI,CAAC;kBAAC+E,KAAK,EAAC9E,CAAC;kBAACgF,kBAAkB,EAAC/E,CAAC;kBAACkB,cAAc,EAACjB;gBAAC,CAAC,GAACZ,CAAC;gBAACkC,CAAC,GAAC,UAAS;kBAACwG,UAAU,EAAC3I,CAAC;kBAACwJ,eAAe,EAACvJ,CAAC;kBAAC6B,cAAc,EAACpB;gBAAC,CAAC,EAAC;kBAAC,OAAM,CAACT,CAAC,IAAEA,CAAC,YAAYwJ,KAAK,KAAGzJ,CAAC,IAAE2B,CAAC,CAAC;oBAACC,KAAK,EAAC,SAAS;oBAACC,QAAQ,EAAC,CAAC,+IAA+I;kBAAC,CAAC,CAAC,EAAC,UAAU,IAAE,OAAO5B,CAAC,CAAC,GAACD,CAAC,GAACA,CAAC,CAAC0J,OAAO,GAAC,KAAK/H,CAAC,CAAC;oBAACE,QAAQ,EAAC,CAAC,0GAA0G,CAAC;oBAACC,cAAc,EAACpB;kBAAC,CAAC,CAAC,GAACT,CAAC,CAAC,CAAC;gBAAA,CAAC,CAAC;kBAAC0I,UAAU,EAACjI,CAAC;kBAAC8I,eAAe,EAACxJ,CAAC;kBAAC8B,cAAc,EAACjB;gBAAC,CAAC,CAAC;cAAC,IAAG,CAACsB,CAAC,EAAC;cAAO,MAAMK,CAAC,GAACL,CAAC,CAACwH,SAAS,CAAC,CAAC,CAAC,CAAC;gBAACjH,CAAC,GAACN,QAAQ,CAACiF,gBAAgB,CAAC,2CAA2C,CAAC;gBAACvG,CAAC,GAAC0B,CAAC,CAAC6E,gBAAgB,CAAC,KAAK,CAAC;gBAACpE,CAAC,GAACT,CAAC,CAAC6E,gBAAgB,CAAC,OAAO,CAAC;gBAAClE,CAAC,GAACxC,CAAC,GAACA,CAAC,CAAC4D,MAAM,GAAC,CAAC;cAAC,OAAM;gBAACK,WAAW,EAACzC,CAAC;gBAAC0C,iBAAiB,EAACrC,CAAC;gBAACuC,cAAc,EAACjE,CAAC;gBAACmE,gBAAgB,EAAChC,CAAC;gBAACkC,kBAAkB,EAAC,CAACvE,CAAC,GAAC,CAAC,GAAC8B,CAAC,CAAC6B,MAAM,IAAEzD,CAAC,CAACyD,MAAM,GAACtB,CAAC,CAACsB,MAAM,GAACpB,CAAC;gBAACkC,mBAAmB,EAAClD,CAAC,CAACkF,gBAAgB,CAAC,QAAQ;cAAC,CAAC;YAAA,CAAC,CAACxG,CAAC,EAACsB,CAAC,CAAC;UAAC,IAAG,CAACqD,CAAC,EAAC,OAAO,KAAK7D,CAAC,CAAC;YAACE,QAAQ,EAAC,CAAC,2BAA2B,CAAC;YAACC,cAAc,EAACwD;UAAC,CAAC,CAAC;UAAC,MAAMI,CAAC,GAAC,UAAS1F,CAAC,EAACC,CAAC,EAACS,CAAC,EAAC;YAAC,MAAK;gBAACoB,cAAc,EAACnB;cAAC,CAAC,GAACX,CAAC;cAACY,CAAC,GAAC,EAAE;cAACC,CAAC,GAAC,EAAE;YAAC,OAAO,UAASsB,CAAC,EAACK,CAAC,EAAC;cAAC5B,CAAC,CAACgJ,QAAQ,CAACzH,CAAC,CAAC,GAACR,CAAC,CAAC;gBAACC,KAAK,EAAC,OAAO;gBAACC,QAAQ,EAAC,CAAC,wDAAwD,EAACM,CAAC,CAAC;gBAACL,cAAc,EAACnB;cAAC,CAAC,CAAC,IAAE6B,CAAC,IAAEb,CAAC,CAAC;gBAACE,QAAQ,EAAC,CAAC,+FAA+F,EAAC,GAAGW,CAAC,CAAC;gBAACV,cAAc,EAACnB;cAAC,CAAC,CAAC,EAACE,CAAC,CAACyD,IAAI,CAACnC,CAAC,CAAC,IAAEvB,CAAC,CAAC0D,IAAI,CAACnC,CAAC,CAAC,EAACvB,CAAC,CAAC2D,MAAM,GAAC1D,CAAC,CAAC0D,MAAM,KAAGtE,CAAC,IAAEyC,CAAC,CAAChC,CAAC,EAACV,CAAC,CAAC,CAAC;YAAA,CAAC;UAAA,CAAC,CAACmC,CAAC,EAACqD,CAAC,CAACL,kBAAkB,EAAC3C,CAAC,CAAC;UAAC,CAAC,UAASxC,CAAC,EAACC,CAAC,EAACS,CAAC,EAACC,CAAC,EAAC;YAACX,CAAC,CAACoG,MAAM,GAAC,MAAI;cAAC7C,CAAC,CAACvD,CAAC,EAACC,CAAC,EAACS,CAAC,EAACC,CAAC,CAAC;YAAA,CAAC,EAACyB,QAAQ,CAACE,IAAI,CAACgE,WAAW,CAACtG,CAAC,CAAC;UAAA,CAAC,CAACwC,CAAC,EAACkD,CAAC,EAACF,CAAC,EAACrD,CAAC,CAAC;QAAA;QAACA,CAAC,CAAC+C,CAAC,EAAC,CAAC,CAAC,CAAC,EAACxD,CAAC,GAACA,CAAC,CAAC,CAAC,CAAC2B,IAAI,CAAE,MAAI;UAACmC,CAAC,CAAC,CAAC;QAAA,CAAE,CAAC,CAAClC,KAAK,CAAEtD,CAAC,IAAE;UAAC,IAAI,IAAE8E,CAAC,IAAEA,CAAC,CAAC,eAAe,EAACtC,CAAC,CAACxC,CAAC,CAAC,CAAC;QAAA,CAAE,CAAC,GAACwF,CAAC,CAAC,CAAC;MAAA,CAAC,EAAE,CAACxF,CAAC,EAACC,CAAC,EAACS,CAAC,EAACC,CAAC,EAACC,CAAC,EAACE,CAAC,EAACmC,CAAC,EAACE,CAAC,EAACzB,CAAC,EAACoD,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,EAACE,CAAC,CAAC,CAAC;IAAA;IAAC,OAAO1E,CAAC;EAAA,CAAC,CAAC,CAAC;AAAA,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}