{"ast": null, "code": "!function (e, t) {\n  \"object\" == typeof exports && \"undefined\" != typeof module ? module.exports = t() : \"function\" == typeof define && define.amd ? define(t) : (e = \"undefined\" != typeof globalThis ? globalThis : e || self).dayjs_plugin_weekOfYear = t();\n}(this, function () {\n  \"use strict\";\n\n  var e = \"week\",\n    t = \"year\";\n  return function (i, n, r) {\n    var f = n.prototype;\n    f.week = function (i) {\n      if (void 0 === i && (i = null), null !== i) return this.add(7 * (i - this.week()), \"day\");\n      var n = this.$locale().yearStart || 1;\n      if (11 === this.month() && this.date() > 25) {\n        var f = r(this).startOf(t).add(1, t).date(n),\n          s = r(this).endOf(e);\n        if (f.isBefore(s)) return 1;\n      }\n      var a = r(this).startOf(t).date(n).startOf(e).subtract(1, \"millisecond\"),\n        o = this.diff(a, e, !0);\n      return o < 0 ? r(this).startOf(\"week\").week() : Math.ceil(o);\n    }, f.weeks = function (e) {\n      return void 0 === e && (e = null), this.week(e);\n    };\n  };\n});", "map": {"version": 3, "names": ["e", "t", "exports", "module", "define", "amd", "globalThis", "self", "dayjs_plugin_weekOfYear", "i", "n", "r", "f", "prototype", "week", "add", "$locale", "yearStart", "month", "date", "startOf", "s", "endOf", "isBefore", "a", "subtract", "o", "diff", "Math", "ceil", "weeks"], "sources": ["D:/My Learning Projects/ucbs_v1_08/frontend/node_modules/dayjs/plugin/weekOfYear.js"], "sourcesContent": ["!function(e,t){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=t():\"function\"==typeof define&&define.amd?define(t):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_plugin_weekOfYear=t()}(this,(function(){\"use strict\";var e=\"week\",t=\"year\";return function(i,n,r){var f=n.prototype;f.week=function(i){if(void 0===i&&(i=null),null!==i)return this.add(7*(i-this.week()),\"day\");var n=this.$locale().yearStart||1;if(11===this.month()&&this.date()>25){var f=r(this).startOf(t).add(1,t).date(n),s=r(this).endOf(e);if(f.isBefore(s))return 1}var a=r(this).startOf(t).date(n).startOf(e).subtract(1,\"millisecond\"),o=this.diff(a,e,!0);return o<0?r(this).startOf(\"week\").week():Math.ceil(o)},f.weeks=function(e){return void 0===e&&(e=null),this.week(e)}}}));"], "mappings": "AAAA,CAAC,UAASA,CAAC,EAACC,CAAC,EAAC;EAAC,QAAQ,IAAE,OAAOC,OAAO,IAAE,WAAW,IAAE,OAAOC,MAAM,GAACA,MAAM,CAACD,OAAO,GAACD,CAAC,CAAC,CAAC,GAAC,UAAU,IAAE,OAAOG,MAAM,IAAEA,MAAM,CAACC,GAAG,GAACD,MAAM,CAACH,CAAC,CAAC,GAAC,CAACD,CAAC,GAAC,WAAW,IAAE,OAAOM,UAAU,GAACA,UAAU,GAACN,CAAC,IAAEO,IAAI,EAAEC,uBAAuB,GAACP,CAAC,CAAC,CAAC;AAAA,CAAC,CAAC,IAAI,EAAE,YAAU;EAAC,YAAY;;EAAC,IAAID,CAAC,GAAC,MAAM;IAACC,CAAC,GAAC,MAAM;EAAC,OAAO,UAASQ,CAAC,EAACC,CAAC,EAACC,CAAC,EAAC;IAAC,IAAIC,CAAC,GAACF,CAAC,CAACG,SAAS;IAACD,CAAC,CAACE,IAAI,GAAC,UAASL,CAAC,EAAC;MAAC,IAAG,KAAK,CAAC,KAAGA,CAAC,KAAGA,CAAC,GAAC,IAAI,CAAC,EAAC,IAAI,KAAGA,CAAC,EAAC,OAAO,IAAI,CAACM,GAAG,CAAC,CAAC,IAAEN,CAAC,GAAC,IAAI,CAACK,IAAI,CAAC,CAAC,CAAC,EAAC,KAAK,CAAC;MAAC,IAAIJ,CAAC,GAAC,IAAI,CAACM,OAAO,CAAC,CAAC,CAACC,SAAS,IAAE,CAAC;MAAC,IAAG,EAAE,KAAG,IAAI,CAACC,KAAK,CAAC,CAAC,IAAE,IAAI,CAACC,IAAI,CAAC,CAAC,GAAC,EAAE,EAAC;QAAC,IAAIP,CAAC,GAACD,CAAC,CAAC,IAAI,CAAC,CAACS,OAAO,CAACnB,CAAC,CAAC,CAACc,GAAG,CAAC,CAAC,EAACd,CAAC,CAAC,CAACkB,IAAI,CAACT,CAAC,CAAC;UAACW,CAAC,GAACV,CAAC,CAAC,IAAI,CAAC,CAACW,KAAK,CAACtB,CAAC,CAAC;QAAC,IAAGY,CAAC,CAACW,QAAQ,CAACF,CAAC,CAAC,EAAC,OAAO,CAAC;MAAA;MAAC,IAAIG,CAAC,GAACb,CAAC,CAAC,IAAI,CAAC,CAACS,OAAO,CAACnB,CAAC,CAAC,CAACkB,IAAI,CAACT,CAAC,CAAC,CAACU,OAAO,CAACpB,CAAC,CAAC,CAACyB,QAAQ,CAAC,CAAC,EAAC,aAAa,CAAC;QAACC,CAAC,GAAC,IAAI,CAACC,IAAI,CAACH,CAAC,EAACxB,CAAC,EAAC,CAAC,CAAC,CAAC;MAAC,OAAO0B,CAAC,GAAC,CAAC,GAACf,CAAC,CAAC,IAAI,CAAC,CAACS,OAAO,CAAC,MAAM,CAAC,CAACN,IAAI,CAAC,CAAC,GAACc,IAAI,CAACC,IAAI,CAACH,CAAC,CAAC;IAAA,CAAC,EAACd,CAAC,CAACkB,KAAK,GAAC,UAAS9B,CAAC,EAAC;MAAC,OAAO,KAAK,CAAC,KAAGA,CAAC,KAAGA,CAAC,GAAC,IAAI,CAAC,EAAC,IAAI,CAACc,IAAI,CAACd,CAAC,CAAC;IAAA,CAAC;EAAA,CAAC;AAAA,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}