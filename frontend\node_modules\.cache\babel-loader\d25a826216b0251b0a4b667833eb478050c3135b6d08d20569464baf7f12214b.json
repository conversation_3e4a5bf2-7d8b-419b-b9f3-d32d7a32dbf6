{"ast": null, "code": "'use client';\n\nexport { default } from './Switch';\nexport { default as switchClasses } from './switchClasses';\nexport * from './switchClasses';", "map": {"version": 3, "names": ["default", "switchClasses"], "sources": ["D:/My Learning Projects/ucbs_v1_08/frontend/node_modules/@mui/material/Switch/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './Switch';\nexport { default as switchClasses } from './switchClasses';\nexport * from './switchClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,UAAU;AAClC,SAASA,OAAO,IAAIC,aAAa,QAAQ,iBAAiB;AAC1D,cAAc,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}