{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar _CODE2 = require('./CODE128');\nvar _CODE3 = _interopRequireDefault(_CODE2);\nvar _auto = require('./auto');\nvar _auto2 = _interopRequireDefault(_auto);\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (!self) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      enumerable: false,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n}\nvar CODE128AUTO = function (_CODE) {\n  _inherits(CODE128AUTO, _CODE);\n  function CODE128AUTO(data, options) {\n    _classCallCheck(this, CODE128AUTO);\n\n    // ASCII value ranges 0-127, 200-211\n    if (/^[\\x00-\\x7F\\xC8-\\xD3]+$/.test(data)) {\n      var _this = _possibleConstructorReturn(this, (CODE128AUTO.__proto__ || Object.getPrototypeOf(CODE128AUTO)).call(this, (0, _auto2.default)(data), options));\n    } else {\n      var _this = _possibleConstructorReturn(this, (CODE128AUTO.__proto__ || Object.getPrototypeOf(CODE128AUTO)).call(this, data, options));\n    }\n    return _possibleConstructorReturn(_this);\n  }\n  return CODE128AUTO;\n}(_CODE3.default);\nexports.default = CODE128AUTO;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "_CODE2", "require", "_CODE3", "_interopRequireDefault", "_auto", "_auto2", "obj", "__esModule", "default", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_possibleConstructorReturn", "self", "call", "ReferenceError", "_inherits", "subClass", "superClass", "prototype", "create", "constructor", "enumerable", "writable", "configurable", "setPrototypeOf", "__proto__", "CODE128AUTO", "_CODE", "data", "options", "test", "_this", "getPrototypeOf"], "sources": ["D:/My Learning Projects/ucbs_v1_08/frontend/node_modules/jsbarcode/bin/barcodes/CODE128/CODE128_AUTO.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\n\nvar _CODE2 = require('./CODE128');\n\nvar _CODE3 = _interopRequireDefault(_CODE2);\n\nvar _auto = require('./auto');\n\nvar _auto2 = _interopRequireDefault(_auto);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }\n\nvar CODE128AUTO = function (_CODE) {\n\t_inherits(CODE128AUTO, _CODE);\n\n\tfunction CODE128AUTO(data, options) {\n\t\t_classCallCheck(this, CODE128AUTO);\n\n\t\t// ASCII value ranges 0-127, 200-211\n\t\tif (/^[\\x00-\\x7F\\xC8-\\xD3]+$/.test(data)) {\n\t\t\tvar _this = _possibleConstructorReturn(this, (CODE128AUTO.__proto__ || Object.getPrototypeOf(CODE128AUTO)).call(this, (0, _auto2.default)(data), options));\n\t\t} else {\n\t\t\tvar _this = _possibleConstructorReturn(this, (CODE128AUTO.__proto__ || Object.getPrototypeOf(CODE128AUTO)).call(this, data, options));\n\t\t}\n\t\treturn _possibleConstructorReturn(_this);\n\t}\n\n\treturn CODE128AUTO;\n}(_CODE3.default);\n\nexports.default = CODE128AUTO;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC5CC,KAAK,EAAE;AACR,CAAC,CAAC;AAEF,IAAIC,MAAM,GAAGC,OAAO,CAAC,WAAW,CAAC;AAEjC,IAAIC,MAAM,GAAGC,sBAAsB,CAACH,MAAM,CAAC;AAE3C,IAAII,KAAK,GAAGH,OAAO,CAAC,QAAQ,CAAC;AAE7B,IAAII,MAAM,GAAGF,sBAAsB,CAACC,KAAK,CAAC;AAE1C,SAASD,sBAAsBA,CAACG,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEE,OAAO,EAAEF;EAAI,CAAC;AAAE;AAE9F,SAASG,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AAExJ,SAASC,0BAA0BA,CAACC,IAAI,EAAEC,IAAI,EAAE;EAAE,IAAI,CAACD,IAAI,EAAE;IAAE,MAAM,IAAIE,cAAc,CAAC,2DAA2D,CAAC;EAAE;EAAE,OAAOD,IAAI,KAAK,OAAOA,IAAI,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,GAAGA,IAAI,GAAGD,IAAI;AAAE;AAE/O,SAASG,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAAE,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAAE,MAAM,IAAIP,SAAS,CAAC,0DAA0D,GAAG,OAAOO,UAAU,CAAC;EAAE;EAAED,QAAQ,CAACE,SAAS,GAAGxB,MAAM,CAACyB,MAAM,CAACF,UAAU,IAAIA,UAAU,CAACC,SAAS,EAAE;IAAEE,WAAW,EAAE;MAAEvB,KAAK,EAAEmB,QAAQ;MAAEK,UAAU,EAAE,KAAK;MAAEC,QAAQ,EAAE,IAAI;MAAEC,YAAY,EAAE;IAAK;EAAE,CAAC,CAAC;EAAE,IAAIN,UAAU,EAAEvB,MAAM,CAAC8B,cAAc,GAAG9B,MAAM,CAAC8B,cAAc,CAACR,QAAQ,EAAEC,UAAU,CAAC,GAAGD,QAAQ,CAACS,SAAS,GAAGR,UAAU;AAAE;AAE7e,IAAIS,WAAW,GAAG,UAAUC,KAAK,EAAE;EAClCZ,SAAS,CAACW,WAAW,EAAEC,KAAK,CAAC;EAE7B,SAASD,WAAWA,CAACE,IAAI,EAAEC,OAAO,EAAE;IACnCtB,eAAe,CAAC,IAAI,EAAEmB,WAAW,CAAC;;IAElC;IACA,IAAI,yBAAyB,CAACI,IAAI,CAACF,IAAI,CAAC,EAAE;MACzC,IAAIG,KAAK,GAAGpB,0BAA0B,CAAC,IAAI,EAAE,CAACe,WAAW,CAACD,SAAS,IAAI/B,MAAM,CAACsC,cAAc,CAACN,WAAW,CAAC,EAAEb,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,EAAEV,MAAM,CAACG,OAAO,EAAEsB,IAAI,CAAC,EAAEC,OAAO,CAAC,CAAC;IAC3J,CAAC,MAAM;MACN,IAAIE,KAAK,GAAGpB,0BAA0B,CAAC,IAAI,EAAE,CAACe,WAAW,CAACD,SAAS,IAAI/B,MAAM,CAACsC,cAAc,CAACN,WAAW,CAAC,EAAEb,IAAI,CAAC,IAAI,EAAEe,IAAI,EAAEC,OAAO,CAAC,CAAC;IACtI;IACA,OAAOlB,0BAA0B,CAACoB,KAAK,CAAC;EACzC;EAEA,OAAOL,WAAW;AACnB,CAAC,CAAC1B,MAAM,CAACM,OAAO,CAAC;AAEjBV,OAAO,CAACU,OAAO,GAAGoB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}