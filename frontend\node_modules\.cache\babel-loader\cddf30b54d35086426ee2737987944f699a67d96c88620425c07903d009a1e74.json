{"ast": null, "code": "/*\n * Copyright 2010 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\nimport DetectorResult from '../common/DetectorResult';\n/**\n * <p>Extends {@link DetectorResult} with more information specific to the Aztec format,\n * like the number of layers and whether it's compact.</p>\n *\n * <AUTHOR> Owen\n */\nvar AztecDetectorResult = /** @class */function (_super) {\n  __extends(AztecDetectorResult, _super);\n  function AztecDetectorResult(bits, points, compact, nbDatablocks, nbLayers) {\n    var _this = _super.call(this, bits, points) || this;\n    _this.compact = compact;\n    _this.nbDatablocks = nbDatablocks;\n    _this.nbLayers = nbLayers;\n    return _this;\n  }\n  AztecDetectorResult.prototype.getNbLayers = function () {\n    return this.nbLayers;\n  };\n  AztecDetectorResult.prototype.getNbDatablocks = function () {\n    return this.nbDatablocks;\n  };\n  AztecDetectorResult.prototype.isCompact = function () {\n    return this.compact;\n  };\n  return AztecDetectorResult;\n}(DetectorResult);\nexport default AztecDetectorResult;", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__", "constructor", "prototype", "create", "DetectorResult", "AztecDetectorResult", "_super", "bits", "points", "compact", "nbDatablocks", "nbLayers", "_this", "call", "getNbLayers", "getNbDatablocks", "isCompact"], "sources": ["D:/My Learning Projects/ucbs_v1_08/frontend/node_modules/@zxing/library/esm/core/aztec/AztecDetectorResult.js"], "sourcesContent": ["/*\n * Copyright 2010 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport DetectorResult from '../common/DetectorResult';\n/**\n * <p>Extends {@link DetectorResult} with more information specific to the Aztec format,\n * like the number of layers and whether it's compact.</p>\n *\n * <AUTHOR> Owen\n */\nvar AztecDetectorResult = /** @class */ (function (_super) {\n    __extends(AztecDetectorResult, _super);\n    function AztecDetectorResult(bits, points, compact, nbDatablocks, nbLayers) {\n        var _this = _super.call(this, bits, points) || this;\n        _this.compact = compact;\n        _this.nbDatablocks = nbDatablocks;\n        _this.nbLayers = nbLayers;\n        return _this;\n    }\n    AztecDetectorResult.prototype.getNbLayers = function () {\n        return this.nbLayers;\n    };\n    AztecDetectorResult.prototype.getNbDatablocks = function () {\n        return this.nbDatablocks;\n    };\n    AztecDetectorResult.prototype.isCompact = function () {\n        return this.compact;\n    };\n    return AztecDetectorResult;\n}(DetectorResult));\nexport default AztecDetectorResult;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIA,CAAC,CAACM,cAAc,CAACD,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IAC9E,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnBF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASO,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGT,CAAC;IAAE;IACtCA,CAAC,CAACU,SAAS,GAAGT,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACS,MAAM,CAACV,CAAC,CAAC,IAAIO,EAAE,CAACE,SAAS,GAAGT,CAAC,CAACS,SAAS,EAAE,IAAIF,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJ,OAAOI,cAAc,MAAM,0BAA0B;AACrD;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,mBAAmB,GAAG,aAAe,UAAUC,MAAM,EAAE;EACvDhB,SAAS,CAACe,mBAAmB,EAAEC,MAAM,CAAC;EACtC,SAASD,mBAAmBA,CAACE,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAEC,YAAY,EAAEC,QAAQ,EAAE;IACxE,IAAIC,KAAK,GAAGN,MAAM,CAACO,IAAI,CAAC,IAAI,EAAEN,IAAI,EAAEC,MAAM,CAAC,IAAI,IAAI;IACnDI,KAAK,CAACH,OAAO,GAAGA,OAAO;IACvBG,KAAK,CAACF,YAAY,GAAGA,YAAY;IACjCE,KAAK,CAACD,QAAQ,GAAGA,QAAQ;IACzB,OAAOC,KAAK;EAChB;EACAP,mBAAmB,CAACH,SAAS,CAACY,WAAW,GAAG,YAAY;IACpD,OAAO,IAAI,CAACH,QAAQ;EACxB,CAAC;EACDN,mBAAmB,CAACH,SAAS,CAACa,eAAe,GAAG,YAAY;IACxD,OAAO,IAAI,CAACL,YAAY;EAC5B,CAAC;EACDL,mBAAmB,CAACH,SAAS,CAACc,SAAS,GAAG,YAAY;IAClD,OAAO,IAAI,CAACP,OAAO;EACvB,CAAC;EACD,OAAOJ,mBAAmB;AAC9B,CAAC,CAACD,cAAc,CAAE;AAClB,eAAeC,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}