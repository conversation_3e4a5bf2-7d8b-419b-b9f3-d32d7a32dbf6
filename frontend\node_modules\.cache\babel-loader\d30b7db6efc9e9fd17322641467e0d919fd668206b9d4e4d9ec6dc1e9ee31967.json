{"ast": null, "code": "/*\n * Copyright (C) 2010 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport AbstractUPCEANReader from './AbstractUPCEANReader';\nimport UPCEANExtension5Support from './UPCEANExtension5Support';\nimport UPCEANExtension2Support from './UPCEANExtension2Support';\nvar UPCEANExtensionSupport = /** @class */function () {\n  function UPCEANExtensionSupport() {}\n  UPCEANExtensionSupport.decodeRow = function (rowNumber, row, rowOffset) {\n    var extensionStartRange = AbstractUPCEANReader.findGuardPattern(row, rowOffset, false, this.EXTENSION_START_PATTERN, new Int32Array(this.EXTENSION_START_PATTERN.length).fill(0));\n    try {\n      // return null;\n      var fiveSupport = new UPCEANExtension5Support();\n      return fiveSupport.decodeRow(rowNumber, row, extensionStartRange);\n    } catch (err) {\n      // return null;\n      var twoSupport = new UPCEANExtension2Support();\n      return twoSupport.decodeRow(rowNumber, row, extensionStartRange);\n    }\n  };\n  UPCEANExtensionSupport.EXTENSION_START_PATTERN = Int32Array.from([1, 1, 2]);\n  return UPCEANExtensionSupport;\n}();\nexport default UPCEANExtensionSupport;", "map": {"version": 3, "names": ["AbstractUPCEANReader", "UPCEANExtension5Support", "UPCEANExtension2Support", "UPCEANExtensionSupport", "decodeRow", "rowNumber", "row", "rowOffset", "extensionStartRange", "<PERSON><PERSON><PERSON><PERSON>att<PERSON>", "EXTENSION_START_PATTERN", "Int32Array", "length", "fill", "fiveSupport", "err", "twoSupport", "from"], "sources": ["D:/My Learning Projects/ucbs_v1_08/frontend/node_modules/@zxing/library/esm/core/oned/UPCEANExtensionSupport.js"], "sourcesContent": ["/*\n * Copyright (C) 2010 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport AbstractUPCEANReader from './AbstractUPCEANReader';\nimport UPCEANExtension5Support from './UPCEANExtension5Support';\nimport UPCEANExtension2Support from './UPCEANExtension2Support';\nvar UPCEANExtensionSupport = /** @class */ (function () {\n    function UPCEANExtensionSupport() {\n    }\n    UPCEANExtensionSupport.decodeRow = function (rowNumber, row, rowOffset) {\n        var extensionStartRange = AbstractUPCEANReader.findGuardPattern(row, rowOffset, false, this.EXTENSION_START_PATTERN, new Int32Array(this.EXTENSION_START_PATTERN.length).fill(0));\n        try {\n            // return null;\n            var fiveSupport = new UPCEANExtension5Support();\n            return fiveSupport.decodeRow(rowNumber, row, extensionStartRange);\n        }\n        catch (err) {\n            // return null;\n            var twoSupport = new UPCEANExtension2Support();\n            return twoSupport.decodeRow(rowNumber, row, extensionStartRange);\n        }\n    };\n    UPCEANExtensionSupport.EXTENSION_START_PATTERN = Int32Array.from([1, 1, 2]);\n    return UPCEANExtensionSupport;\n}());\nexport default UPCEANExtensionSupport;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,oBAAoB,MAAM,wBAAwB;AACzD,OAAOC,uBAAuB,MAAM,2BAA2B;AAC/D,OAAOC,uBAAuB,MAAM,2BAA2B;AAC/D,IAAIC,sBAAsB,GAAG,aAAe,YAAY;EACpD,SAASA,sBAAsBA,CAAA,EAAG,CAClC;EACAA,sBAAsB,CAACC,SAAS,GAAG,UAAUC,SAAS,EAAEC,GAAG,EAAEC,SAAS,EAAE;IACpE,IAAIC,mBAAmB,GAAGR,oBAAoB,CAACS,gBAAgB,CAACH,GAAG,EAAEC,SAAS,EAAE,KAAK,EAAE,IAAI,CAACG,uBAAuB,EAAE,IAAIC,UAAU,CAAC,IAAI,CAACD,uBAAuB,CAACE,MAAM,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC;IACjL,IAAI;MACA;MACA,IAAIC,WAAW,GAAG,IAAIb,uBAAuB,CAAC,CAAC;MAC/C,OAAOa,WAAW,CAACV,SAAS,CAACC,SAAS,EAAEC,GAAG,EAAEE,mBAAmB,CAAC;IACrE,CAAC,CACD,OAAOO,GAAG,EAAE;MACR;MACA,IAAIC,UAAU,GAAG,IAAId,uBAAuB,CAAC,CAAC;MAC9C,OAAOc,UAAU,CAACZ,SAAS,CAACC,SAAS,EAAEC,GAAG,EAAEE,mBAAmB,CAAC;IACpE;EACJ,CAAC;EACDL,sBAAsB,CAACO,uBAAuB,GAAGC,UAAU,CAACM,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EAC3E,OAAOd,sBAAsB;AACjC,CAAC,CAAC,CAAE;AACJ,eAAeA,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}