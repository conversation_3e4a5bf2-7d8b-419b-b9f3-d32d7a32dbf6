{"ast": null, "code": "import DecoderResult from '../../common/DecoderResult';\nimport BitSource from '../../common/BitSource';\nimport StringBuilder from '../../util/StringBuilder';\nimport StringEncoding from '../../util/StringEncoding';\nimport StringUtils from '../../common/StringUtils';\nimport FormatException from '../../FormatException';\nimport IllegalStateException from '../../IllegalStateException';\n/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar Mode;\n(function (Mode) {\n  Mode[Mode[\"PAD_ENCODE\"] = 0] = \"PAD_ENCODE\";\n  Mode[Mode[\"ASCII_ENCODE\"] = 1] = \"ASCII_ENCODE\";\n  Mode[Mode[\"C40_ENCODE\"] = 2] = \"C40_ENCODE\";\n  Mode[Mode[\"TEXT_ENCODE\"] = 3] = \"TEXT_ENCODE\";\n  Mode[Mode[\"ANSIX12_ENCODE\"] = 4] = \"ANSIX12_ENCODE\";\n  Mode[Mode[\"EDIFACT_ENCODE\"] = 5] = \"EDIFACT_ENCODE\";\n  Mode[Mode[\"BASE256_ENCODE\"] = 6] = \"BASE256_ENCODE\";\n})(Mode || (Mode = {}));\n/**\n * <p>Data Matrix Codes can encode text as bits in one of several modes, and can use multiple modes\n * in one Data Matrix Code. This class decodes the bits back into text.</p>\n *\n * <p>See ISO 16022:2006, 5.2.1 - *******</p>\n *\n * <AUTHOR> (Brian Brown)\n * <AUTHOR> Owen\n */\nvar DecodedBitStreamParser = /** @class */function () {\n  function DecodedBitStreamParser() {}\n  DecodedBitStreamParser.decode = function (bytes) {\n    var bits = new BitSource(bytes);\n    var result = new StringBuilder();\n    var resultTrailer = new StringBuilder();\n    var byteSegments = new Array();\n    var mode = Mode.ASCII_ENCODE;\n    do {\n      if (mode === Mode.ASCII_ENCODE) {\n        mode = this.decodeAsciiSegment(bits, result, resultTrailer);\n      } else {\n        switch (mode) {\n          case Mode.C40_ENCODE:\n            this.decodeC40Segment(bits, result);\n            break;\n          case Mode.TEXT_ENCODE:\n            this.decodeTextSegment(bits, result);\n            break;\n          case Mode.ANSIX12_ENCODE:\n            this.decodeAnsiX12Segment(bits, result);\n            break;\n          case Mode.EDIFACT_ENCODE:\n            this.decodeEdifactSegment(bits, result);\n            break;\n          case Mode.BASE256_ENCODE:\n            this.decodeBase256Segment(bits, result, byteSegments);\n            break;\n          default:\n            throw new FormatException();\n        }\n        mode = Mode.ASCII_ENCODE;\n      }\n    } while (mode !== Mode.PAD_ENCODE && bits.available() > 0);\n    if (resultTrailer.length() > 0) {\n      result.append(resultTrailer.toString());\n    }\n    return new DecoderResult(bytes, result.toString(), byteSegments.length === 0 ? null : byteSegments, null);\n  };\n  /**\n   * See ISO 16022:2006, 5.2.3 and Annex C, Table C.2\n   */\n  DecodedBitStreamParser.decodeAsciiSegment = function (bits, result, resultTrailer) {\n    var upperShift = false;\n    do {\n      var oneByte = bits.readBits(8);\n      if (oneByte === 0) {\n        throw new FormatException();\n      } else if (oneByte <= 128) {\n        // ASCII data (ASCII value + 1)\n        if (upperShift) {\n          oneByte += 128;\n          // upperShift = false;\n        }\n        result.append(String.fromCharCode(oneByte - 1));\n        return Mode.ASCII_ENCODE;\n      } else if (oneByte === 129) {\n        // Pad\n        return Mode.PAD_ENCODE;\n      } else if (oneByte <= 229) {\n        // 2-digit data 00-99 (Numeric Value + 130)\n        var value = oneByte - 130;\n        if (value < 10) {\n          // pad with '0' for single digit values\n          result.append('0');\n        }\n        result.append('' + value);\n      } else {\n        switch (oneByte) {\n          case 230:\n            // Latch to C40 encodation\n            return Mode.C40_ENCODE;\n          case 231:\n            // Latch to Base 256 encodation\n            return Mode.BASE256_ENCODE;\n          case 232:\n            // FNC1\n            result.append(String.fromCharCode(29)); // translate as ASCII 29\n            break;\n          case 233: // Structured Append\n          case 234:\n            // Reader Programming\n            // Ignore these symbols for now\n            // throw ReaderException.getInstance();\n            break;\n          case 235:\n            // Upper Shift (shift to Extended ASCII)\n            upperShift = true;\n            break;\n          case 236:\n            // 05 Macro\n            result.append('[)>\\u001E05\\u001D');\n            resultTrailer.insert(0, '\\u001E\\u0004');\n            break;\n          case 237:\n            // 06 Macro\n            result.append('[)>\\u001E06\\u001D');\n            resultTrailer.insert(0, '\\u001E\\u0004');\n            break;\n          case 238:\n            // Latch to ANSI X12 encodation\n            return Mode.ANSIX12_ENCODE;\n          case 239:\n            // Latch to Text encodation\n            return Mode.TEXT_ENCODE;\n          case 240:\n            // Latch to EDIFACT encodation\n            return Mode.EDIFACT_ENCODE;\n          case 241:\n            // ECI Character\n            // TODO(bbrown): I think we need to support ECI\n            // throw ReaderException.getInstance();\n            // Ignore this symbol for now\n            break;\n          default:\n            // Not to be used in ASCII encodation\n            // but work around encoders that end with 254, latch back to ASCII\n            if (oneByte !== 254 || bits.available() !== 0) {\n              throw new FormatException();\n            }\n            break;\n        }\n      }\n    } while (bits.available() > 0);\n    return Mode.ASCII_ENCODE;\n  };\n  /**\n   * See ISO 16022:2006, 5.2.5 and Annex C, Table C.1\n   */\n  DecodedBitStreamParser.decodeC40Segment = function (bits, result) {\n    // Three C40 values are encoded in a 16-bit value as\n    // (1600 * C1) + (40 * C2) + C3 + 1\n    // TODO(bbrown): The Upper Shift with C40 doesn't work in the 4 value scenario all the time\n    var upperShift = false;\n    var cValues = [];\n    var shift = 0;\n    do {\n      // If there is only one byte left then it will be encoded as ASCII\n      if (bits.available() === 8) {\n        return;\n      }\n      var firstByte = bits.readBits(8);\n      if (firstByte === 254) {\n        // Unlatch codeword\n        return;\n      }\n      this.parseTwoBytes(firstByte, bits.readBits(8), cValues);\n      for (var i = 0; i < 3; i++) {\n        var cValue = cValues[i];\n        switch (shift) {\n          case 0:\n            if (cValue < 3) {\n              shift = cValue + 1;\n            } else if (cValue < this.C40_BASIC_SET_CHARS.length) {\n              var c40char = this.C40_BASIC_SET_CHARS[cValue];\n              if (upperShift) {\n                result.append(String.fromCharCode(c40char.charCodeAt(0) + 128));\n                upperShift = false;\n              } else {\n                result.append(c40char);\n              }\n            } else {\n              throw new FormatException();\n            }\n            break;\n          case 1:\n            if (upperShift) {\n              result.append(String.fromCharCode(cValue + 128));\n              upperShift = false;\n            } else {\n              result.append(String.fromCharCode(cValue));\n            }\n            shift = 0;\n            break;\n          case 2:\n            if (cValue < this.C40_SHIFT2_SET_CHARS.length) {\n              var c40char = this.C40_SHIFT2_SET_CHARS[cValue];\n              if (upperShift) {\n                result.append(String.fromCharCode(c40char.charCodeAt(0) + 128));\n                upperShift = false;\n              } else {\n                result.append(c40char);\n              }\n            } else {\n              switch (cValue) {\n                case 27:\n                  // FNC1\n                  result.append(String.fromCharCode(29)); // translate as ASCII 29\n                  break;\n                case 30:\n                  // Upper Shift\n                  upperShift = true;\n                  break;\n                default:\n                  throw new FormatException();\n              }\n            }\n            shift = 0;\n            break;\n          case 3:\n            if (upperShift) {\n              result.append(String.fromCharCode(cValue + 224));\n              upperShift = false;\n            } else {\n              result.append(String.fromCharCode(cValue + 96));\n            }\n            shift = 0;\n            break;\n          default:\n            throw new FormatException();\n        }\n      }\n    } while (bits.available() > 0);\n  };\n  /**\n   * See ISO 16022:2006, 5.2.6 and Annex C, Table C.2\n   */\n  DecodedBitStreamParser.decodeTextSegment = function (bits, result) {\n    // Three Text values are encoded in a 16-bit value as\n    // (1600 * C1) + (40 * C2) + C3 + 1\n    // TODO(bbrown): The Upper Shift with Text doesn't work in the 4 value scenario all the time\n    var upperShift = false;\n    var cValues = [];\n    var shift = 0;\n    do {\n      // If there is only one byte left then it will be encoded as ASCII\n      if (bits.available() === 8) {\n        return;\n      }\n      var firstByte = bits.readBits(8);\n      if (firstByte === 254) {\n        // Unlatch codeword\n        return;\n      }\n      this.parseTwoBytes(firstByte, bits.readBits(8), cValues);\n      for (var i = 0; i < 3; i++) {\n        var cValue = cValues[i];\n        switch (shift) {\n          case 0:\n            if (cValue < 3) {\n              shift = cValue + 1;\n            } else if (cValue < this.TEXT_BASIC_SET_CHARS.length) {\n              var textChar = this.TEXT_BASIC_SET_CHARS[cValue];\n              if (upperShift) {\n                result.append(String.fromCharCode(textChar.charCodeAt(0) + 128));\n                upperShift = false;\n              } else {\n                result.append(textChar);\n              }\n            } else {\n              throw new FormatException();\n            }\n            break;\n          case 1:\n            if (upperShift) {\n              result.append(String.fromCharCode(cValue + 128));\n              upperShift = false;\n            } else {\n              result.append(String.fromCharCode(cValue));\n            }\n            shift = 0;\n            break;\n          case 2:\n            // Shift 2 for Text is the same encoding as C40\n            if (cValue < this.TEXT_SHIFT2_SET_CHARS.length) {\n              var textChar = this.TEXT_SHIFT2_SET_CHARS[cValue];\n              if (upperShift) {\n                result.append(String.fromCharCode(textChar.charCodeAt(0) + 128));\n                upperShift = false;\n              } else {\n                result.append(textChar);\n              }\n            } else {\n              switch (cValue) {\n                case 27:\n                  // FNC1\n                  result.append(String.fromCharCode(29)); // translate as ASCII 29\n                  break;\n                case 30:\n                  // Upper Shift\n                  upperShift = true;\n                  break;\n                default:\n                  throw new FormatException();\n              }\n            }\n            shift = 0;\n            break;\n          case 3:\n            if (cValue < this.TEXT_SHIFT3_SET_CHARS.length) {\n              var textChar = this.TEXT_SHIFT3_SET_CHARS[cValue];\n              if (upperShift) {\n                result.append(String.fromCharCode(textChar.charCodeAt(0) + 128));\n                upperShift = false;\n              } else {\n                result.append(textChar);\n              }\n              shift = 0;\n            } else {\n              throw new FormatException();\n            }\n            break;\n          default:\n            throw new FormatException();\n        }\n      }\n    } while (bits.available() > 0);\n  };\n  /**\n   * See ISO 16022:2006, 5.2.7\n   */\n  DecodedBitStreamParser.decodeAnsiX12Segment = function (bits, result) {\n    // Three ANSI X12 values are encoded in a 16-bit value as\n    // (1600 * C1) + (40 * C2) + C3 + 1\n    var cValues = [];\n    do {\n      // If there is only one byte left then it will be encoded as ASCII\n      if (bits.available() === 8) {\n        return;\n      }\n      var firstByte = bits.readBits(8);\n      if (firstByte === 254) {\n        // Unlatch codeword\n        return;\n      }\n      this.parseTwoBytes(firstByte, bits.readBits(8), cValues);\n      for (var i = 0; i < 3; i++) {\n        var cValue = cValues[i];\n        switch (cValue) {\n          case 0:\n            // X12 segment terminator <CR>\n            result.append('\\r');\n            break;\n          case 1:\n            // X12 segment separator *\n            result.append('*');\n            break;\n          case 2:\n            // X12 sub-element separator >\n            result.append('>');\n            break;\n          case 3:\n            // space\n            result.append(' ');\n            break;\n          default:\n            if (cValue < 14) {\n              // 0 - 9\n              result.append(String.fromCharCode(cValue + 44));\n            } else if (cValue < 40) {\n              // A - Z\n              result.append(String.fromCharCode(cValue + 51));\n            } else {\n              throw new FormatException();\n            }\n            break;\n        }\n      }\n    } while (bits.available() > 0);\n  };\n  DecodedBitStreamParser.parseTwoBytes = function (firstByte, secondByte, result) {\n    var fullBitValue = (firstByte << 8) + secondByte - 1;\n    var temp = Math.floor(fullBitValue / 1600);\n    result[0] = temp;\n    fullBitValue -= temp * 1600;\n    temp = Math.floor(fullBitValue / 40);\n    result[1] = temp;\n    result[2] = fullBitValue - temp * 40;\n  };\n  /**\n   * See ISO 16022:2006, 5.2.8 and Annex C Table C.3\n   */\n  DecodedBitStreamParser.decodeEdifactSegment = function (bits, result) {\n    do {\n      // If there is only two or less bytes left then it will be encoded as ASCII\n      if (bits.available() <= 16) {\n        return;\n      }\n      for (var i = 0; i < 4; i++) {\n        var edifactValue = bits.readBits(6);\n        // Check for the unlatch character\n        if (edifactValue === 0x1F) {\n          // 011111\n          // Read rest of byte, which should be 0, and stop\n          var bitsLeft = 8 - bits.getBitOffset();\n          if (bitsLeft !== 8) {\n            bits.readBits(bitsLeft);\n          }\n          return;\n        }\n        if ((edifactValue & 0x20) === 0) {\n          // no 1 in the leading (6th) bit\n          edifactValue |= 0x40; // Add a leading 01 to the 6 bit binary value\n        }\n        result.append(String.fromCharCode(edifactValue));\n      }\n    } while (bits.available() > 0);\n  };\n  /**\n   * See ISO 16022:2006, 5.2.9 and Annex B, B.2\n   */\n  DecodedBitStreamParser.decodeBase256Segment = function (bits, result, byteSegments) {\n    // Figure out how long the Base 256 Segment is.\n    var codewordPosition = 1 + bits.getByteOffset(); // position is 1-indexed\n    var d1 = this.unrandomize255State(bits.readBits(8), codewordPosition++);\n    var count;\n    if (d1 === 0) {\n      // Read the remainder of the symbol\n      count = bits.available() / 8 | 0;\n    } else if (d1 < 250) {\n      count = d1;\n    } else {\n      count = 250 * (d1 - 249) + this.unrandomize255State(bits.readBits(8), codewordPosition++);\n    }\n    // We're seeing NegativeArraySizeException errors from users.\n    if (count < 0) {\n      throw new FormatException();\n    }\n    var bytes = new Uint8Array(count);\n    for (var i = 0; i < count; i++) {\n      // Have seen this particular error in the wild, such as at\n      // http://www.bcgen.com/demo/IDAutomationStreamingDataMatrix.aspx?MODE=3&D=Fred&PFMT=3&PT=F&X=0.3&O=0&LM=0.2\n      if (bits.available() < 8) {\n        throw new FormatException();\n      }\n      bytes[i] = this.unrandomize255State(bits.readBits(8), codewordPosition++);\n    }\n    byteSegments.push(bytes);\n    try {\n      result.append(StringEncoding.decode(bytes, StringUtils.ISO88591));\n    } catch (uee) {\n      throw new IllegalStateException('Platform does not support required encoding: ' + uee.message);\n    }\n  };\n  /**\n   * See ISO 16022:2006, Annex B, B.2\n   */\n  DecodedBitStreamParser.unrandomize255State = function (randomizedBase256Codeword, base256CodewordPosition) {\n    var pseudoRandomNumber = 149 * base256CodewordPosition % 255 + 1;\n    var tempVariable = randomizedBase256Codeword - pseudoRandomNumber;\n    return tempVariable >= 0 ? tempVariable : tempVariable + 256;\n  };\n  /**\n   * See ISO 16022:2006, Annex C Table C.1\n   * The C40 Basic Character Set (*'s used for placeholders for the shift values)\n   */\n  DecodedBitStreamParser.C40_BASIC_SET_CHARS = ['*', '*', '*', ' ', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];\n  DecodedBitStreamParser.C40_SHIFT2_SET_CHARS = ['!', '\"', '#', '$', '%', '&', '\\'', '(', ')', '*', '+', ',', '-', '.', '/', ':', ';', '<', '=', '>', '?', '@', '[', '\\\\', ']', '^', '_'];\n  /**\n   * See ISO 16022:2006, Annex C Table C.2\n   * The Text Basic Character Set (*'s used for placeholders for the shift values)\n   */\n  DecodedBitStreamParser.TEXT_BASIC_SET_CHARS = ['*', '*', '*', ' ', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z'];\n  // Shift 2 for Text is the same encoding as C40\n  DecodedBitStreamParser.TEXT_SHIFT2_SET_CHARS = DecodedBitStreamParser.C40_SHIFT2_SET_CHARS;\n  DecodedBitStreamParser.TEXT_SHIFT3_SET_CHARS = ['`', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', '{', '|', '}', '~', String.fromCharCode(127)];\n  return DecodedBitStreamParser;\n}();\nexport default DecodedBitStreamParser;", "map": {"version": 3, "names": ["DecoderResult", "BitSource", "StringBuilder", "StringEncoding", "StringUtils", "FormatException", "IllegalStateException", "Mode", "DecodedBitStreamParser", "decode", "bytes", "bits", "result", "resultTrailer", "byteSegments", "Array", "mode", "ASCII_ENCODE", "decodeAsciiSegment", "C40_ENCODE", "decodeC40Segment", "TEXT_ENCODE", "decodeTextSegment", "ANSIX12_ENCODE", "decodeAnsiX12Segment", "EDIFACT_ENCODE", "decodeEdifactSegment", "BASE256_ENCODE", "decodeBase256Segment", "PAD_ENCODE", "available", "length", "append", "toString", "upperShift", "oneByte", "readBits", "String", "fromCharCode", "value", "insert", "c<PERSON><PERSON><PERSON>", "shift", "firstByte", "parseTwoBytes", "i", "cValue", "C40_BASIC_SET_CHARS", "c40char", "charCodeAt", "C40_SHIFT2_SET_CHARS", "TEXT_BASIC_SET_CHARS", "textChar", "TEXT_SHIFT2_SET_CHARS", "TEXT_SHIFT3_SET_CHARS", "secondByte", "fullBitValue", "temp", "Math", "floor", "edifactValue", "bitsLeft", "getBitOffset", "codewordPosition", "getByteOffset", "d1", "unrandomize255State", "count", "Uint8Array", "push", "ISO88591", "uee", "message", "randomizedBase256Codeword", "base256CodewordPosition", "pseudoRandomNumber", "tempVariable"], "sources": ["D:/My Learning Projects/ucbs_v1_08/frontend/node_modules/@zxing/library/esm/core/datamatrix/decoder/DecodedBitStreamParser.js"], "sourcesContent": ["import DecoderResult from '../../common/DecoderResult';\nimport BitSource from '../../common/BitSource';\nimport StringBuilder from '../../util/StringBuilder';\nimport StringEncoding from '../../util/StringEncoding';\nimport StringUtils from '../../common/StringUtils';\nimport FormatException from '../../FormatException';\nimport IllegalStateException from '../../IllegalStateException';\n/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar Mode;\n(function (Mode) {\n    Mode[Mode[\"PAD_ENCODE\"] = 0] = \"PAD_ENCODE\";\n    Mode[Mode[\"ASCII_ENCODE\"] = 1] = \"ASCII_ENCODE\";\n    Mode[Mode[\"C40_ENCODE\"] = 2] = \"C40_ENCODE\";\n    Mode[Mode[\"TEXT_ENCODE\"] = 3] = \"TEXT_ENCODE\";\n    Mode[Mode[\"ANSIX12_ENCODE\"] = 4] = \"ANSIX12_ENCODE\";\n    Mode[Mode[\"EDIFACT_ENCODE\"] = 5] = \"EDIFACT_ENCODE\";\n    Mode[Mode[\"BASE256_ENCODE\"] = 6] = \"BASE256_ENCODE\";\n})(Mode || (Mode = {}));\n/**\n * <p>Data Matrix Codes can encode text as bits in one of several modes, and can use multiple modes\n * in one Data Matrix Code. This class decodes the bits back into text.</p>\n *\n * <p>See ISO 16022:2006, 5.2.1 - *******</p>\n *\n * <AUTHOR> (Brian Brown)\n * <AUTHOR> Owen\n */\nvar DecodedBitStreamParser = /** @class */ (function () {\n    function DecodedBitStreamParser() {\n    }\n    DecodedBitStreamParser.decode = function (bytes) {\n        var bits = new BitSource(bytes);\n        var result = new StringBuilder();\n        var resultTrailer = new StringBuilder();\n        var byteSegments = new Array();\n        var mode = Mode.ASCII_ENCODE;\n        do {\n            if (mode === Mode.ASCII_ENCODE) {\n                mode = this.decodeAsciiSegment(bits, result, resultTrailer);\n            }\n            else {\n                switch (mode) {\n                    case Mode.C40_ENCODE:\n                        this.decodeC40Segment(bits, result);\n                        break;\n                    case Mode.TEXT_ENCODE:\n                        this.decodeTextSegment(bits, result);\n                        break;\n                    case Mode.ANSIX12_ENCODE:\n                        this.decodeAnsiX12Segment(bits, result);\n                        break;\n                    case Mode.EDIFACT_ENCODE:\n                        this.decodeEdifactSegment(bits, result);\n                        break;\n                    case Mode.BASE256_ENCODE:\n                        this.decodeBase256Segment(bits, result, byteSegments);\n                        break;\n                    default:\n                        throw new FormatException();\n                }\n                mode = Mode.ASCII_ENCODE;\n            }\n        } while (mode !== Mode.PAD_ENCODE && bits.available() > 0);\n        if (resultTrailer.length() > 0) {\n            result.append(resultTrailer.toString());\n        }\n        return new DecoderResult(bytes, result.toString(), byteSegments.length === 0 ? null : byteSegments, null);\n    };\n    /**\n     * See ISO 16022:2006, 5.2.3 and Annex C, Table C.2\n     */\n    DecodedBitStreamParser.decodeAsciiSegment = function (bits, result, resultTrailer) {\n        var upperShift = false;\n        do {\n            var oneByte = bits.readBits(8);\n            if (oneByte === 0) {\n                throw new FormatException();\n            }\n            else if (oneByte <= 128) { // ASCII data (ASCII value + 1)\n                if (upperShift) {\n                    oneByte += 128;\n                    // upperShift = false;\n                }\n                result.append(String.fromCharCode(oneByte - 1));\n                return Mode.ASCII_ENCODE;\n            }\n            else if (oneByte === 129) { // Pad\n                return Mode.PAD_ENCODE;\n            }\n            else if (oneByte <= 229) { // 2-digit data 00-99 (Numeric Value + 130)\n                var value = oneByte - 130;\n                if (value < 10) { // pad with '0' for single digit values\n                    result.append('0');\n                }\n                result.append('' + value);\n            }\n            else {\n                switch (oneByte) {\n                    case 230: // Latch to C40 encodation\n                        return Mode.C40_ENCODE;\n                    case 231: // Latch to Base 256 encodation\n                        return Mode.BASE256_ENCODE;\n                    case 232: // FNC1\n                        result.append(String.fromCharCode(29)); // translate as ASCII 29\n                        break;\n                    case 233: // Structured Append\n                    case 234: // Reader Programming\n                        // Ignore these symbols for now\n                        // throw ReaderException.getInstance();\n                        break;\n                    case 235: // Upper Shift (shift to Extended ASCII)\n                        upperShift = true;\n                        break;\n                    case 236: // 05 Macro\n                        result.append('[)>\\u001E05\\u001D');\n                        resultTrailer.insert(0, '\\u001E\\u0004');\n                        break;\n                    case 237: // 06 Macro\n                        result.append('[)>\\u001E06\\u001D');\n                        resultTrailer.insert(0, '\\u001E\\u0004');\n                        break;\n                    case 238: // Latch to ANSI X12 encodation\n                        return Mode.ANSIX12_ENCODE;\n                    case 239: // Latch to Text encodation\n                        return Mode.TEXT_ENCODE;\n                    case 240: // Latch to EDIFACT encodation\n                        return Mode.EDIFACT_ENCODE;\n                    case 241: // ECI Character\n                        // TODO(bbrown): I think we need to support ECI\n                        // throw ReaderException.getInstance();\n                        // Ignore this symbol for now\n                        break;\n                    default:\n                        // Not to be used in ASCII encodation\n                        // but work around encoders that end with 254, latch back to ASCII\n                        if (oneByte !== 254 || bits.available() !== 0) {\n                            throw new FormatException();\n                        }\n                        break;\n                }\n            }\n        } while (bits.available() > 0);\n        return Mode.ASCII_ENCODE;\n    };\n    /**\n     * See ISO 16022:2006, 5.2.5 and Annex C, Table C.1\n     */\n    DecodedBitStreamParser.decodeC40Segment = function (bits, result) {\n        // Three C40 values are encoded in a 16-bit value as\n        // (1600 * C1) + (40 * C2) + C3 + 1\n        // TODO(bbrown): The Upper Shift with C40 doesn't work in the 4 value scenario all the time\n        var upperShift = false;\n        var cValues = [];\n        var shift = 0;\n        do {\n            // If there is only one byte left then it will be encoded as ASCII\n            if (bits.available() === 8) {\n                return;\n            }\n            var firstByte = bits.readBits(8);\n            if (firstByte === 254) { // Unlatch codeword\n                return;\n            }\n            this.parseTwoBytes(firstByte, bits.readBits(8), cValues);\n            for (var i = 0; i < 3; i++) {\n                var cValue = cValues[i];\n                switch (shift) {\n                    case 0:\n                        if (cValue < 3) {\n                            shift = cValue + 1;\n                        }\n                        else if (cValue < this.C40_BASIC_SET_CHARS.length) {\n                            var c40char = this.C40_BASIC_SET_CHARS[cValue];\n                            if (upperShift) {\n                                result.append(String.fromCharCode(c40char.charCodeAt(0) + 128));\n                                upperShift = false;\n                            }\n                            else {\n                                result.append(c40char);\n                            }\n                        }\n                        else {\n                            throw new FormatException();\n                        }\n                        break;\n                    case 1:\n                        if (upperShift) {\n                            result.append(String.fromCharCode(cValue + 128));\n                            upperShift = false;\n                        }\n                        else {\n                            result.append(String.fromCharCode(cValue));\n                        }\n                        shift = 0;\n                        break;\n                    case 2:\n                        if (cValue < this.C40_SHIFT2_SET_CHARS.length) {\n                            var c40char = this.C40_SHIFT2_SET_CHARS[cValue];\n                            if (upperShift) {\n                                result.append(String.fromCharCode(c40char.charCodeAt(0) + 128));\n                                upperShift = false;\n                            }\n                            else {\n                                result.append(c40char);\n                            }\n                        }\n                        else {\n                            switch (cValue) {\n                                case 27: // FNC1\n                                    result.append(String.fromCharCode(29)); // translate as ASCII 29\n                                    break;\n                                case 30: // Upper Shift\n                                    upperShift = true;\n                                    break;\n                                default:\n                                    throw new FormatException();\n                            }\n                        }\n                        shift = 0;\n                        break;\n                    case 3:\n                        if (upperShift) {\n                            result.append(String.fromCharCode(cValue + 224));\n                            upperShift = false;\n                        }\n                        else {\n                            result.append(String.fromCharCode(cValue + 96));\n                        }\n                        shift = 0;\n                        break;\n                    default:\n                        throw new FormatException();\n                }\n            }\n        } while (bits.available() > 0);\n    };\n    /**\n     * See ISO 16022:2006, 5.2.6 and Annex C, Table C.2\n     */\n    DecodedBitStreamParser.decodeTextSegment = function (bits, result) {\n        // Three Text values are encoded in a 16-bit value as\n        // (1600 * C1) + (40 * C2) + C3 + 1\n        // TODO(bbrown): The Upper Shift with Text doesn't work in the 4 value scenario all the time\n        var upperShift = false;\n        var cValues = [];\n        var shift = 0;\n        do {\n            // If there is only one byte left then it will be encoded as ASCII\n            if (bits.available() === 8) {\n                return;\n            }\n            var firstByte = bits.readBits(8);\n            if (firstByte === 254) { // Unlatch codeword\n                return;\n            }\n            this.parseTwoBytes(firstByte, bits.readBits(8), cValues);\n            for (var i = 0; i < 3; i++) {\n                var cValue = cValues[i];\n                switch (shift) {\n                    case 0:\n                        if (cValue < 3) {\n                            shift = cValue + 1;\n                        }\n                        else if (cValue < this.TEXT_BASIC_SET_CHARS.length) {\n                            var textChar = this.TEXT_BASIC_SET_CHARS[cValue];\n                            if (upperShift) {\n                                result.append(String.fromCharCode(textChar.charCodeAt(0) + 128));\n                                upperShift = false;\n                            }\n                            else {\n                                result.append(textChar);\n                            }\n                        }\n                        else {\n                            throw new FormatException();\n                        }\n                        break;\n                    case 1:\n                        if (upperShift) {\n                            result.append(String.fromCharCode(cValue + 128));\n                            upperShift = false;\n                        }\n                        else {\n                            result.append(String.fromCharCode(cValue));\n                        }\n                        shift = 0;\n                        break;\n                    case 2:\n                        // Shift 2 for Text is the same encoding as C40\n                        if (cValue < this.TEXT_SHIFT2_SET_CHARS.length) {\n                            var textChar = this.TEXT_SHIFT2_SET_CHARS[cValue];\n                            if (upperShift) {\n                                result.append(String.fromCharCode(textChar.charCodeAt(0) + 128));\n                                upperShift = false;\n                            }\n                            else {\n                                result.append(textChar);\n                            }\n                        }\n                        else {\n                            switch (cValue) {\n                                case 27: // FNC1\n                                    result.append(String.fromCharCode(29)); // translate as ASCII 29\n                                    break;\n                                case 30: // Upper Shift\n                                    upperShift = true;\n                                    break;\n                                default:\n                                    throw new FormatException();\n                            }\n                        }\n                        shift = 0;\n                        break;\n                    case 3:\n                        if (cValue < this.TEXT_SHIFT3_SET_CHARS.length) {\n                            var textChar = this.TEXT_SHIFT3_SET_CHARS[cValue];\n                            if (upperShift) {\n                                result.append(String.fromCharCode(textChar.charCodeAt(0) + 128));\n                                upperShift = false;\n                            }\n                            else {\n                                result.append(textChar);\n                            }\n                            shift = 0;\n                        }\n                        else {\n                            throw new FormatException();\n                        }\n                        break;\n                    default:\n                        throw new FormatException();\n                }\n            }\n        } while (bits.available() > 0);\n    };\n    /**\n     * See ISO 16022:2006, 5.2.7\n     */\n    DecodedBitStreamParser.decodeAnsiX12Segment = function (bits, result) {\n        // Three ANSI X12 values are encoded in a 16-bit value as\n        // (1600 * C1) + (40 * C2) + C3 + 1\n        var cValues = [];\n        do {\n            // If there is only one byte left then it will be encoded as ASCII\n            if (bits.available() === 8) {\n                return;\n            }\n            var firstByte = bits.readBits(8);\n            if (firstByte === 254) { // Unlatch codeword\n                return;\n            }\n            this.parseTwoBytes(firstByte, bits.readBits(8), cValues);\n            for (var i = 0; i < 3; i++) {\n                var cValue = cValues[i];\n                switch (cValue) {\n                    case 0: // X12 segment terminator <CR>\n                        result.append('\\r');\n                        break;\n                    case 1: // X12 segment separator *\n                        result.append('*');\n                        break;\n                    case 2: // X12 sub-element separator >\n                        result.append('>');\n                        break;\n                    case 3: // space\n                        result.append(' ');\n                        break;\n                    default:\n                        if (cValue < 14) { // 0 - 9\n                            result.append(String.fromCharCode(cValue + 44));\n                        }\n                        else if (cValue < 40) { // A - Z\n                            result.append(String.fromCharCode(cValue + 51));\n                        }\n                        else {\n                            throw new FormatException();\n                        }\n                        break;\n                }\n            }\n        } while (bits.available() > 0);\n    };\n    DecodedBitStreamParser.parseTwoBytes = function (firstByte, secondByte, result) {\n        var fullBitValue = (firstByte << 8) + secondByte - 1;\n        var temp = Math.floor(fullBitValue / 1600);\n        result[0] = temp;\n        fullBitValue -= temp * 1600;\n        temp = Math.floor(fullBitValue / 40);\n        result[1] = temp;\n        result[2] = fullBitValue - temp * 40;\n    };\n    /**\n     * See ISO 16022:2006, 5.2.8 and Annex C Table C.3\n     */\n    DecodedBitStreamParser.decodeEdifactSegment = function (bits, result) {\n        do {\n            // If there is only two or less bytes left then it will be encoded as ASCII\n            if (bits.available() <= 16) {\n                return;\n            }\n            for (var i = 0; i < 4; i++) {\n                var edifactValue = bits.readBits(6);\n                // Check for the unlatch character\n                if (edifactValue === 0x1F) { // 011111\n                    // Read rest of byte, which should be 0, and stop\n                    var bitsLeft = 8 - bits.getBitOffset();\n                    if (bitsLeft !== 8) {\n                        bits.readBits(bitsLeft);\n                    }\n                    return;\n                }\n                if ((edifactValue & 0x20) === 0) { // no 1 in the leading (6th) bit\n                    edifactValue |= 0x40; // Add a leading 01 to the 6 bit binary value\n                }\n                result.append(String.fromCharCode(edifactValue));\n            }\n        } while (bits.available() > 0);\n    };\n    /**\n     * See ISO 16022:2006, 5.2.9 and Annex B, B.2\n     */\n    DecodedBitStreamParser.decodeBase256Segment = function (bits, result, byteSegments) {\n        // Figure out how long the Base 256 Segment is.\n        var codewordPosition = 1 + bits.getByteOffset(); // position is 1-indexed\n        var d1 = this.unrandomize255State(bits.readBits(8), codewordPosition++);\n        var count;\n        if (d1 === 0) { // Read the remainder of the symbol\n            count = bits.available() / 8 | 0;\n        }\n        else if (d1 < 250) {\n            count = d1;\n        }\n        else {\n            count = 250 * (d1 - 249) + this.unrandomize255State(bits.readBits(8), codewordPosition++);\n        }\n        // We're seeing NegativeArraySizeException errors from users.\n        if (count < 0) {\n            throw new FormatException();\n        }\n        var bytes = new Uint8Array(count);\n        for (var i = 0; i < count; i++) {\n            // Have seen this particular error in the wild, such as at\n            // http://www.bcgen.com/demo/IDAutomationStreamingDataMatrix.aspx?MODE=3&D=Fred&PFMT=3&PT=F&X=0.3&O=0&LM=0.2\n            if (bits.available() < 8) {\n                throw new FormatException();\n            }\n            bytes[i] = this.unrandomize255State(bits.readBits(8), codewordPosition++);\n        }\n        byteSegments.push(bytes);\n        try {\n            result.append(StringEncoding.decode(bytes, StringUtils.ISO88591));\n        }\n        catch (uee) {\n            throw new IllegalStateException('Platform does not support required encoding: ' + uee.message);\n        }\n    };\n    /**\n     * See ISO 16022:2006, Annex B, B.2\n     */\n    DecodedBitStreamParser.unrandomize255State = function (randomizedBase256Codeword, base256CodewordPosition) {\n        var pseudoRandomNumber = ((149 * base256CodewordPosition) % 255) + 1;\n        var tempVariable = randomizedBase256Codeword - pseudoRandomNumber;\n        return tempVariable >= 0 ? tempVariable : tempVariable + 256;\n    };\n    /**\n     * See ISO 16022:2006, Annex C Table C.1\n     * The C40 Basic Character Set (*'s used for placeholders for the shift values)\n     */\n    DecodedBitStreamParser.C40_BASIC_SET_CHARS = [\n        '*', '*', '*', ' ', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',\n        'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N',\n        'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'\n    ];\n    DecodedBitStreamParser.C40_SHIFT2_SET_CHARS = [\n        '!', '\"', '#', '$', '%', '&', '\\'', '(', ')', '*', '+', ',', '-', '.',\n        '/', ':', ';', '<', '=', '>', '?', '@', '[', '\\\\', ']', '^', '_'\n    ];\n    /**\n     * See ISO 16022:2006, Annex C Table C.2\n     * The Text Basic Character Set (*'s used for placeholders for the shift values)\n     */\n    DecodedBitStreamParser.TEXT_BASIC_SET_CHARS = [\n        '*', '*', '*', ' ', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',\n        'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n',\n        'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z'\n    ];\n    // Shift 2 for Text is the same encoding as C40\n    DecodedBitStreamParser.TEXT_SHIFT2_SET_CHARS = DecodedBitStreamParser.C40_SHIFT2_SET_CHARS;\n    DecodedBitStreamParser.TEXT_SHIFT3_SET_CHARS = [\n        '`', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N',\n        'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', '{', '|', '}', '~', String.fromCharCode(127)\n    ];\n    return DecodedBitStreamParser;\n}());\nexport default DecodedBitStreamParser;\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,4BAA4B;AACtD,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,aAAa,MAAM,0BAA0B;AACpD,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,eAAe,MAAM,uBAAuB;AACnD,OAAOC,qBAAqB,MAAM,6BAA6B;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,IAAI;AACR,CAAC,UAAUA,IAAI,EAAE;EACbA,IAAI,CAACA,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY;EAC3CA,IAAI,CAACA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,GAAG,cAAc;EAC/CA,IAAI,CAACA,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY;EAC3CA,IAAI,CAACA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,GAAG,aAAa;EAC7CA,IAAI,CAACA,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,GAAG,gBAAgB;EACnDA,IAAI,CAACA,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,GAAG,gBAAgB;EACnDA,IAAI,CAACA,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,GAAG,gBAAgB;AACvD,CAAC,EAAEA,IAAI,KAAKA,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,sBAAsB,GAAG,aAAe,YAAY;EACpD,SAASA,sBAAsBA,CAAA,EAAG,CAClC;EACAA,sBAAsB,CAACC,MAAM,GAAG,UAAUC,KAAK,EAAE;IAC7C,IAAIC,IAAI,GAAG,IAAIV,SAAS,CAACS,KAAK,CAAC;IAC/B,IAAIE,MAAM,GAAG,IAAIV,aAAa,CAAC,CAAC;IAChC,IAAIW,aAAa,GAAG,IAAIX,aAAa,CAAC,CAAC;IACvC,IAAIY,YAAY,GAAG,IAAIC,KAAK,CAAC,CAAC;IAC9B,IAAIC,IAAI,GAAGT,IAAI,CAACU,YAAY;IAC5B,GAAG;MACC,IAAID,IAAI,KAAKT,IAAI,CAACU,YAAY,EAAE;QAC5BD,IAAI,GAAG,IAAI,CAACE,kBAAkB,CAACP,IAAI,EAAEC,MAAM,EAAEC,aAAa,CAAC;MAC/D,CAAC,MACI;QACD,QAAQG,IAAI;UACR,KAAKT,IAAI,CAACY,UAAU;YAChB,IAAI,CAACC,gBAAgB,CAACT,IAAI,EAAEC,MAAM,CAAC;YACnC;UACJ,KAAKL,IAAI,CAACc,WAAW;YACjB,IAAI,CAACC,iBAAiB,CAACX,IAAI,EAAEC,MAAM,CAAC;YACpC;UACJ,KAAKL,IAAI,CAACgB,cAAc;YACpB,IAAI,CAACC,oBAAoB,CAACb,IAAI,EAAEC,MAAM,CAAC;YACvC;UACJ,KAAKL,IAAI,CAACkB,cAAc;YACpB,IAAI,CAACC,oBAAoB,CAACf,IAAI,EAAEC,MAAM,CAAC;YACvC;UACJ,KAAKL,IAAI,CAACoB,cAAc;YACpB,IAAI,CAACC,oBAAoB,CAACjB,IAAI,EAAEC,MAAM,EAAEE,YAAY,CAAC;YACrD;UACJ;YACI,MAAM,IAAIT,eAAe,CAAC,CAAC;QACnC;QACAW,IAAI,GAAGT,IAAI,CAACU,YAAY;MAC5B;IACJ,CAAC,QAAQD,IAAI,KAAKT,IAAI,CAACsB,UAAU,IAAIlB,IAAI,CAACmB,SAAS,CAAC,CAAC,GAAG,CAAC;IACzD,IAAIjB,aAAa,CAACkB,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE;MAC5BnB,MAAM,CAACoB,MAAM,CAACnB,aAAa,CAACoB,QAAQ,CAAC,CAAC,CAAC;IAC3C;IACA,OAAO,IAAIjC,aAAa,CAACU,KAAK,EAAEE,MAAM,CAACqB,QAAQ,CAAC,CAAC,EAAEnB,YAAY,CAACiB,MAAM,KAAK,CAAC,GAAG,IAAI,GAAGjB,YAAY,EAAE,IAAI,CAAC;EAC7G,CAAC;EACD;AACJ;AACA;EACIN,sBAAsB,CAACU,kBAAkB,GAAG,UAAUP,IAAI,EAAEC,MAAM,EAAEC,aAAa,EAAE;IAC/E,IAAIqB,UAAU,GAAG,KAAK;IACtB,GAAG;MACC,IAAIC,OAAO,GAAGxB,IAAI,CAACyB,QAAQ,CAAC,CAAC,CAAC;MAC9B,IAAID,OAAO,KAAK,CAAC,EAAE;QACf,MAAM,IAAI9B,eAAe,CAAC,CAAC;MAC/B,CAAC,MACI,IAAI8B,OAAO,IAAI,GAAG,EAAE;QAAE;QACvB,IAAID,UAAU,EAAE;UACZC,OAAO,IAAI,GAAG;UACd;QACJ;QACAvB,MAAM,CAACoB,MAAM,CAACK,MAAM,CAACC,YAAY,CAACH,OAAO,GAAG,CAAC,CAAC,CAAC;QAC/C,OAAO5B,IAAI,CAACU,YAAY;MAC5B,CAAC,MACI,IAAIkB,OAAO,KAAK,GAAG,EAAE;QAAE;QACxB,OAAO5B,IAAI,CAACsB,UAAU;MAC1B,CAAC,MACI,IAAIM,OAAO,IAAI,GAAG,EAAE;QAAE;QACvB,IAAII,KAAK,GAAGJ,OAAO,GAAG,GAAG;QACzB,IAAII,KAAK,GAAG,EAAE,EAAE;UAAE;UACd3B,MAAM,CAACoB,MAAM,CAAC,GAAG,CAAC;QACtB;QACApB,MAAM,CAACoB,MAAM,CAAC,EAAE,GAAGO,KAAK,CAAC;MAC7B,CAAC,MACI;QACD,QAAQJ,OAAO;UACX,KAAK,GAAG;YAAE;YACN,OAAO5B,IAAI,CAACY,UAAU;UAC1B,KAAK,GAAG;YAAE;YACN,OAAOZ,IAAI,CAACoB,cAAc;UAC9B,KAAK,GAAG;YAAE;YACNf,MAAM,CAACoB,MAAM,CAACK,MAAM,CAACC,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACxC;UACJ,KAAK,GAAG,CAAC,CAAC;UACV,KAAK,GAAG;YAAE;YACN;YACA;YACA;UACJ,KAAK,GAAG;YAAE;YACNJ,UAAU,GAAG,IAAI;YACjB;UACJ,KAAK,GAAG;YAAE;YACNtB,MAAM,CAACoB,MAAM,CAAC,mBAAmB,CAAC;YAClCnB,aAAa,CAAC2B,MAAM,CAAC,CAAC,EAAE,cAAc,CAAC;YACvC;UACJ,KAAK,GAAG;YAAE;YACN5B,MAAM,CAACoB,MAAM,CAAC,mBAAmB,CAAC;YAClCnB,aAAa,CAAC2B,MAAM,CAAC,CAAC,EAAE,cAAc,CAAC;YACvC;UACJ,KAAK,GAAG;YAAE;YACN,OAAOjC,IAAI,CAACgB,cAAc;UAC9B,KAAK,GAAG;YAAE;YACN,OAAOhB,IAAI,CAACc,WAAW;UAC3B,KAAK,GAAG;YAAE;YACN,OAAOd,IAAI,CAACkB,cAAc;UAC9B,KAAK,GAAG;YAAE;YACN;YACA;YACA;YACA;UACJ;YACI;YACA;YACA,IAAIU,OAAO,KAAK,GAAG,IAAIxB,IAAI,CAACmB,SAAS,CAAC,CAAC,KAAK,CAAC,EAAE;cAC3C,MAAM,IAAIzB,eAAe,CAAC,CAAC;YAC/B;YACA;QACR;MACJ;IACJ,CAAC,QAAQM,IAAI,CAACmB,SAAS,CAAC,CAAC,GAAG,CAAC;IAC7B,OAAOvB,IAAI,CAACU,YAAY;EAC5B,CAAC;EACD;AACJ;AACA;EACIT,sBAAsB,CAACY,gBAAgB,GAAG,UAAUT,IAAI,EAAEC,MAAM,EAAE;IAC9D;IACA;IACA;IACA,IAAIsB,UAAU,GAAG,KAAK;IACtB,IAAIO,OAAO,GAAG,EAAE;IAChB,IAAIC,KAAK,GAAG,CAAC;IACb,GAAG;MACC;MACA,IAAI/B,IAAI,CAACmB,SAAS,CAAC,CAAC,KAAK,CAAC,EAAE;QACxB;MACJ;MACA,IAAIa,SAAS,GAAGhC,IAAI,CAACyB,QAAQ,CAAC,CAAC,CAAC;MAChC,IAAIO,SAAS,KAAK,GAAG,EAAE;QAAE;QACrB;MACJ;MACA,IAAI,CAACC,aAAa,CAACD,SAAS,EAAEhC,IAAI,CAACyB,QAAQ,CAAC,CAAC,CAAC,EAAEK,OAAO,CAAC;MACxD,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;QACxB,IAAIC,MAAM,GAAGL,OAAO,CAACI,CAAC,CAAC;QACvB,QAAQH,KAAK;UACT,KAAK,CAAC;YACF,IAAII,MAAM,GAAG,CAAC,EAAE;cACZJ,KAAK,GAAGI,MAAM,GAAG,CAAC;YACtB,CAAC,MACI,IAAIA,MAAM,GAAG,IAAI,CAACC,mBAAmB,CAAChB,MAAM,EAAE;cAC/C,IAAIiB,OAAO,GAAG,IAAI,CAACD,mBAAmB,CAACD,MAAM,CAAC;cAC9C,IAAIZ,UAAU,EAAE;gBACZtB,MAAM,CAACoB,MAAM,CAACK,MAAM,CAACC,YAAY,CAACU,OAAO,CAACC,UAAU,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;gBAC/Df,UAAU,GAAG,KAAK;cACtB,CAAC,MACI;gBACDtB,MAAM,CAACoB,MAAM,CAACgB,OAAO,CAAC;cAC1B;YACJ,CAAC,MACI;cACD,MAAM,IAAI3C,eAAe,CAAC,CAAC;YAC/B;YACA;UACJ,KAAK,CAAC;YACF,IAAI6B,UAAU,EAAE;cACZtB,MAAM,CAACoB,MAAM,CAACK,MAAM,CAACC,YAAY,CAACQ,MAAM,GAAG,GAAG,CAAC,CAAC;cAChDZ,UAAU,GAAG,KAAK;YACtB,CAAC,MACI;cACDtB,MAAM,CAACoB,MAAM,CAACK,MAAM,CAACC,YAAY,CAACQ,MAAM,CAAC,CAAC;YAC9C;YACAJ,KAAK,GAAG,CAAC;YACT;UACJ,KAAK,CAAC;YACF,IAAII,MAAM,GAAG,IAAI,CAACI,oBAAoB,CAACnB,MAAM,EAAE;cAC3C,IAAIiB,OAAO,GAAG,IAAI,CAACE,oBAAoB,CAACJ,MAAM,CAAC;cAC/C,IAAIZ,UAAU,EAAE;gBACZtB,MAAM,CAACoB,MAAM,CAACK,MAAM,CAACC,YAAY,CAACU,OAAO,CAACC,UAAU,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;gBAC/Df,UAAU,GAAG,KAAK;cACtB,CAAC,MACI;gBACDtB,MAAM,CAACoB,MAAM,CAACgB,OAAO,CAAC;cAC1B;YACJ,CAAC,MACI;cACD,QAAQF,MAAM;gBACV,KAAK,EAAE;kBAAE;kBACLlC,MAAM,CAACoB,MAAM,CAACK,MAAM,CAACC,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;kBACxC;gBACJ,KAAK,EAAE;kBAAE;kBACLJ,UAAU,GAAG,IAAI;kBACjB;gBACJ;kBACI,MAAM,IAAI7B,eAAe,CAAC,CAAC;cACnC;YACJ;YACAqC,KAAK,GAAG,CAAC;YACT;UACJ,KAAK,CAAC;YACF,IAAIR,UAAU,EAAE;cACZtB,MAAM,CAACoB,MAAM,CAACK,MAAM,CAACC,YAAY,CAACQ,MAAM,GAAG,GAAG,CAAC,CAAC;cAChDZ,UAAU,GAAG,KAAK;YACtB,CAAC,MACI;cACDtB,MAAM,CAACoB,MAAM,CAACK,MAAM,CAACC,YAAY,CAACQ,MAAM,GAAG,EAAE,CAAC,CAAC;YACnD;YACAJ,KAAK,GAAG,CAAC;YACT;UACJ;YACI,MAAM,IAAIrC,eAAe,CAAC,CAAC;QACnC;MACJ;IACJ,CAAC,QAAQM,IAAI,CAACmB,SAAS,CAAC,CAAC,GAAG,CAAC;EACjC,CAAC;EACD;AACJ;AACA;EACItB,sBAAsB,CAACc,iBAAiB,GAAG,UAAUX,IAAI,EAAEC,MAAM,EAAE;IAC/D;IACA;IACA;IACA,IAAIsB,UAAU,GAAG,KAAK;IACtB,IAAIO,OAAO,GAAG,EAAE;IAChB,IAAIC,KAAK,GAAG,CAAC;IACb,GAAG;MACC;MACA,IAAI/B,IAAI,CAACmB,SAAS,CAAC,CAAC,KAAK,CAAC,EAAE;QACxB;MACJ;MACA,IAAIa,SAAS,GAAGhC,IAAI,CAACyB,QAAQ,CAAC,CAAC,CAAC;MAChC,IAAIO,SAAS,KAAK,GAAG,EAAE;QAAE;QACrB;MACJ;MACA,IAAI,CAACC,aAAa,CAACD,SAAS,EAAEhC,IAAI,CAACyB,QAAQ,CAAC,CAAC,CAAC,EAAEK,OAAO,CAAC;MACxD,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;QACxB,IAAIC,MAAM,GAAGL,OAAO,CAACI,CAAC,CAAC;QACvB,QAAQH,KAAK;UACT,KAAK,CAAC;YACF,IAAII,MAAM,GAAG,CAAC,EAAE;cACZJ,KAAK,GAAGI,MAAM,GAAG,CAAC;YACtB,CAAC,MACI,IAAIA,MAAM,GAAG,IAAI,CAACK,oBAAoB,CAACpB,MAAM,EAAE;cAChD,IAAIqB,QAAQ,GAAG,IAAI,CAACD,oBAAoB,CAACL,MAAM,CAAC;cAChD,IAAIZ,UAAU,EAAE;gBACZtB,MAAM,CAACoB,MAAM,CAACK,MAAM,CAACC,YAAY,CAACc,QAAQ,CAACH,UAAU,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;gBAChEf,UAAU,GAAG,KAAK;cACtB,CAAC,MACI;gBACDtB,MAAM,CAACoB,MAAM,CAACoB,QAAQ,CAAC;cAC3B;YACJ,CAAC,MACI;cACD,MAAM,IAAI/C,eAAe,CAAC,CAAC;YAC/B;YACA;UACJ,KAAK,CAAC;YACF,IAAI6B,UAAU,EAAE;cACZtB,MAAM,CAACoB,MAAM,CAACK,MAAM,CAACC,YAAY,CAACQ,MAAM,GAAG,GAAG,CAAC,CAAC;cAChDZ,UAAU,GAAG,KAAK;YACtB,CAAC,MACI;cACDtB,MAAM,CAACoB,MAAM,CAACK,MAAM,CAACC,YAAY,CAACQ,MAAM,CAAC,CAAC;YAC9C;YACAJ,KAAK,GAAG,CAAC;YACT;UACJ,KAAK,CAAC;YACF;YACA,IAAII,MAAM,GAAG,IAAI,CAACO,qBAAqB,CAACtB,MAAM,EAAE;cAC5C,IAAIqB,QAAQ,GAAG,IAAI,CAACC,qBAAqB,CAACP,MAAM,CAAC;cACjD,IAAIZ,UAAU,EAAE;gBACZtB,MAAM,CAACoB,MAAM,CAACK,MAAM,CAACC,YAAY,CAACc,QAAQ,CAACH,UAAU,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;gBAChEf,UAAU,GAAG,KAAK;cACtB,CAAC,MACI;gBACDtB,MAAM,CAACoB,MAAM,CAACoB,QAAQ,CAAC;cAC3B;YACJ,CAAC,MACI;cACD,QAAQN,MAAM;gBACV,KAAK,EAAE;kBAAE;kBACLlC,MAAM,CAACoB,MAAM,CAACK,MAAM,CAACC,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;kBACxC;gBACJ,KAAK,EAAE;kBAAE;kBACLJ,UAAU,GAAG,IAAI;kBACjB;gBACJ;kBACI,MAAM,IAAI7B,eAAe,CAAC,CAAC;cACnC;YACJ;YACAqC,KAAK,GAAG,CAAC;YACT;UACJ,KAAK,CAAC;YACF,IAAII,MAAM,GAAG,IAAI,CAACQ,qBAAqB,CAACvB,MAAM,EAAE;cAC5C,IAAIqB,QAAQ,GAAG,IAAI,CAACE,qBAAqB,CAACR,MAAM,CAAC;cACjD,IAAIZ,UAAU,EAAE;gBACZtB,MAAM,CAACoB,MAAM,CAACK,MAAM,CAACC,YAAY,CAACc,QAAQ,CAACH,UAAU,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;gBAChEf,UAAU,GAAG,KAAK;cACtB,CAAC,MACI;gBACDtB,MAAM,CAACoB,MAAM,CAACoB,QAAQ,CAAC;cAC3B;cACAV,KAAK,GAAG,CAAC;YACb,CAAC,MACI;cACD,MAAM,IAAIrC,eAAe,CAAC,CAAC;YAC/B;YACA;UACJ;YACI,MAAM,IAAIA,eAAe,CAAC,CAAC;QACnC;MACJ;IACJ,CAAC,QAAQM,IAAI,CAACmB,SAAS,CAAC,CAAC,GAAG,CAAC;EACjC,CAAC;EACD;AACJ;AACA;EACItB,sBAAsB,CAACgB,oBAAoB,GAAG,UAAUb,IAAI,EAAEC,MAAM,EAAE;IAClE;IACA;IACA,IAAI6B,OAAO,GAAG,EAAE;IAChB,GAAG;MACC;MACA,IAAI9B,IAAI,CAACmB,SAAS,CAAC,CAAC,KAAK,CAAC,EAAE;QACxB;MACJ;MACA,IAAIa,SAAS,GAAGhC,IAAI,CAACyB,QAAQ,CAAC,CAAC,CAAC;MAChC,IAAIO,SAAS,KAAK,GAAG,EAAE;QAAE;QACrB;MACJ;MACA,IAAI,CAACC,aAAa,CAACD,SAAS,EAAEhC,IAAI,CAACyB,QAAQ,CAAC,CAAC,CAAC,EAAEK,OAAO,CAAC;MACxD,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;QACxB,IAAIC,MAAM,GAAGL,OAAO,CAACI,CAAC,CAAC;QACvB,QAAQC,MAAM;UACV,KAAK,CAAC;YAAE;YACJlC,MAAM,CAACoB,MAAM,CAAC,IAAI,CAAC;YACnB;UACJ,KAAK,CAAC;YAAE;YACJpB,MAAM,CAACoB,MAAM,CAAC,GAAG,CAAC;YAClB;UACJ,KAAK,CAAC;YAAE;YACJpB,MAAM,CAACoB,MAAM,CAAC,GAAG,CAAC;YAClB;UACJ,KAAK,CAAC;YAAE;YACJpB,MAAM,CAACoB,MAAM,CAAC,GAAG,CAAC;YAClB;UACJ;YACI,IAAIc,MAAM,GAAG,EAAE,EAAE;cAAE;cACflC,MAAM,CAACoB,MAAM,CAACK,MAAM,CAACC,YAAY,CAACQ,MAAM,GAAG,EAAE,CAAC,CAAC;YACnD,CAAC,MACI,IAAIA,MAAM,GAAG,EAAE,EAAE;cAAE;cACpBlC,MAAM,CAACoB,MAAM,CAACK,MAAM,CAACC,YAAY,CAACQ,MAAM,GAAG,EAAE,CAAC,CAAC;YACnD,CAAC,MACI;cACD,MAAM,IAAIzC,eAAe,CAAC,CAAC;YAC/B;YACA;QACR;MACJ;IACJ,CAAC,QAAQM,IAAI,CAACmB,SAAS,CAAC,CAAC,GAAG,CAAC;EACjC,CAAC;EACDtB,sBAAsB,CAACoC,aAAa,GAAG,UAAUD,SAAS,EAAEY,UAAU,EAAE3C,MAAM,EAAE;IAC5E,IAAI4C,YAAY,GAAG,CAACb,SAAS,IAAI,CAAC,IAAIY,UAAU,GAAG,CAAC;IACpD,IAAIE,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,YAAY,GAAG,IAAI,CAAC;IAC1C5C,MAAM,CAAC,CAAC,CAAC,GAAG6C,IAAI;IAChBD,YAAY,IAAIC,IAAI,GAAG,IAAI;IAC3BA,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,YAAY,GAAG,EAAE,CAAC;IACpC5C,MAAM,CAAC,CAAC,CAAC,GAAG6C,IAAI;IAChB7C,MAAM,CAAC,CAAC,CAAC,GAAG4C,YAAY,GAAGC,IAAI,GAAG,EAAE;EACxC,CAAC;EACD;AACJ;AACA;EACIjD,sBAAsB,CAACkB,oBAAoB,GAAG,UAAUf,IAAI,EAAEC,MAAM,EAAE;IAClE,GAAG;MACC;MACA,IAAID,IAAI,CAACmB,SAAS,CAAC,CAAC,IAAI,EAAE,EAAE;QACxB;MACJ;MACA,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;QACxB,IAAIe,YAAY,GAAGjD,IAAI,CAACyB,QAAQ,CAAC,CAAC,CAAC;QACnC;QACA,IAAIwB,YAAY,KAAK,IAAI,EAAE;UAAE;UACzB;UACA,IAAIC,QAAQ,GAAG,CAAC,GAAGlD,IAAI,CAACmD,YAAY,CAAC,CAAC;UACtC,IAAID,QAAQ,KAAK,CAAC,EAAE;YAChBlD,IAAI,CAACyB,QAAQ,CAACyB,QAAQ,CAAC;UAC3B;UACA;QACJ;QACA,IAAI,CAACD,YAAY,GAAG,IAAI,MAAM,CAAC,EAAE;UAAE;UAC/BA,YAAY,IAAI,IAAI,CAAC,CAAC;QAC1B;QACAhD,MAAM,CAACoB,MAAM,CAACK,MAAM,CAACC,YAAY,CAACsB,YAAY,CAAC,CAAC;MACpD;IACJ,CAAC,QAAQjD,IAAI,CAACmB,SAAS,CAAC,CAAC,GAAG,CAAC;EACjC,CAAC;EACD;AACJ;AACA;EACItB,sBAAsB,CAACoB,oBAAoB,GAAG,UAAUjB,IAAI,EAAEC,MAAM,EAAEE,YAAY,EAAE;IAChF;IACA,IAAIiD,gBAAgB,GAAG,CAAC,GAAGpD,IAAI,CAACqD,aAAa,CAAC,CAAC,CAAC,CAAC;IACjD,IAAIC,EAAE,GAAG,IAAI,CAACC,mBAAmB,CAACvD,IAAI,CAACyB,QAAQ,CAAC,CAAC,CAAC,EAAE2B,gBAAgB,EAAE,CAAC;IACvE,IAAII,KAAK;IACT,IAAIF,EAAE,KAAK,CAAC,EAAE;MAAE;MACZE,KAAK,GAAGxD,IAAI,CAACmB,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;IACpC,CAAC,MACI,IAAImC,EAAE,GAAG,GAAG,EAAE;MACfE,KAAK,GAAGF,EAAE;IACd,CAAC,MACI;MACDE,KAAK,GAAG,GAAG,IAAIF,EAAE,GAAG,GAAG,CAAC,GAAG,IAAI,CAACC,mBAAmB,CAACvD,IAAI,CAACyB,QAAQ,CAAC,CAAC,CAAC,EAAE2B,gBAAgB,EAAE,CAAC;IAC7F;IACA;IACA,IAAII,KAAK,GAAG,CAAC,EAAE;MACX,MAAM,IAAI9D,eAAe,CAAC,CAAC;IAC/B;IACA,IAAIK,KAAK,GAAG,IAAI0D,UAAU,CAACD,KAAK,CAAC;IACjC,KAAK,IAAItB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsB,KAAK,EAAEtB,CAAC,EAAE,EAAE;MAC5B;MACA;MACA,IAAIlC,IAAI,CAACmB,SAAS,CAAC,CAAC,GAAG,CAAC,EAAE;QACtB,MAAM,IAAIzB,eAAe,CAAC,CAAC;MAC/B;MACAK,KAAK,CAACmC,CAAC,CAAC,GAAG,IAAI,CAACqB,mBAAmB,CAACvD,IAAI,CAACyB,QAAQ,CAAC,CAAC,CAAC,EAAE2B,gBAAgB,EAAE,CAAC;IAC7E;IACAjD,YAAY,CAACuD,IAAI,CAAC3D,KAAK,CAAC;IACxB,IAAI;MACAE,MAAM,CAACoB,MAAM,CAAC7B,cAAc,CAACM,MAAM,CAACC,KAAK,EAAEN,WAAW,CAACkE,QAAQ,CAAC,CAAC;IACrE,CAAC,CACD,OAAOC,GAAG,EAAE;MACR,MAAM,IAAIjE,qBAAqB,CAAC,+CAA+C,GAAGiE,GAAG,CAACC,OAAO,CAAC;IAClG;EACJ,CAAC;EACD;AACJ;AACA;EACIhE,sBAAsB,CAAC0D,mBAAmB,GAAG,UAAUO,yBAAyB,EAAEC,uBAAuB,EAAE;IACvG,IAAIC,kBAAkB,GAAK,GAAG,GAAGD,uBAAuB,GAAI,GAAG,GAAI,CAAC;IACpE,IAAIE,YAAY,GAAGH,yBAAyB,GAAGE,kBAAkB;IACjE,OAAOC,YAAY,IAAI,CAAC,GAAGA,YAAY,GAAGA,YAAY,GAAG,GAAG;EAChE,CAAC;EACD;AACJ;AACA;AACA;EACIpE,sBAAsB,CAACuC,mBAAmB,GAAG,CACzC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EACpE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EACpE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAC7D;EACDvC,sBAAsB,CAAC0C,oBAAoB,GAAG,CAC1C,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EACrE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CACnE;EACD;AACJ;AACA;AACA;EACI1C,sBAAsB,CAAC2C,oBAAoB,GAAG,CAC1C,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EACpE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EACpE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAC7D;EACD;EACA3C,sBAAsB,CAAC6C,qBAAqB,GAAG7C,sBAAsB,CAAC0C,oBAAoB;EAC1F1C,sBAAsB,CAAC8C,qBAAqB,GAAG,CAC3C,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EACzE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAEjB,MAAM,CAACC,YAAY,CAAC,GAAG,CAAC,CAC3G;EACD,OAAO9B,sBAAsB;AACjC,CAAC,CAAC,CAAE;AACJ,eAAeA,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}