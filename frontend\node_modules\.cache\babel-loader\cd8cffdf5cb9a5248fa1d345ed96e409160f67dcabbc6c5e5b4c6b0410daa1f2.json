{"ast": null, "code": "/**\n * Permission checking utilities for frontend\n */\n\n/**\n * Get current user from localStorage\n */\nexport const getCurrentUser = () => {\n  try {\n    const userStr = localStorage.getItem('currentUser');\n    return userStr ? JSON.parse(userStr) : null;\n  } catch (error) {\n    console.error('Error getting current user:', error);\n    return null;\n  }\n};\n\n/**\n * Check if user has a specific permission\n * @param {string} permissionName - The permission to check (e.g., 'customers.create')\n * @returns {boolean} - True if user has permission\n */\nexport const hasPermission = permissionName => {\n  const user = getCurrentUser();\n\n  // If no user, no permission\n  if (!user || !user.role) {\n    return false;\n  }\n\n  // Developer role has all permissions\n  if (user.role.name === 'developer') {\n    return true;\n  }\n\n  // Check if user has the specific permission\n  const userPermissions = user.role.permissions || [];\n  return userPermissions.some(permission => permission.name === permissionName);\n};\n\n/**\n * Check if user has any of the specified permissions\n * @param {string[]} permissionNames - Array of permissions to check\n * @returns {boolean} - True if user has any of the permissions\n */\nexport const hasAnyPermission = permissionNames => {\n  return permissionNames.some(permission => hasPermission(permission));\n};\n\n/**\n * Check if user has all of the specified permissions\n * @param {string[]} permissionNames - Array of permissions to check\n * @returns {boolean} - True if user has all permissions\n */\nexport const hasAllPermissions = permissionNames => {\n  return permissionNames.every(permission => hasPermission(permission));\n};\n\n/**\n * Get polite permission denied messages\n */\nexport const getPermissionMessages = () => ({\n  create: \"You don't have permission to create new items. Please contact your administrator if you need this access.\",\n  edit: \"You don't have permission to edit items. Please contact your administrator if you need this access.\",\n  delete: \"You don't have permission to delete items. Please contact your administrator if you need this access.\",\n  view: \"You don't have permission to view this information. Please contact your administrator if you need this access.\",\n  general: \"You don't have permission to perform this action. Please contact your administrator if you need this access.\"\n});\n\n/**\n * Permission-aware component wrapper\n * @param {string} permission - Required permission\n * @param {React.Component} children - Component to render if permission exists\n * @param {React.Component} fallback - Component to render if no permission (optional)\n */\nexport const PermissionWrapper = ({\n  permission,\n  children,\n  fallback = null\n}) => {\n  if (hasPermission(permission)) {\n    return children;\n  }\n  return fallback;\n};\n\n/**\n * Get user's role information\n */\n_c = PermissionWrapper;\nexport const getUserRole = () => {\n  const user = getCurrentUser();\n  return (user === null || user === void 0 ? void 0 : user.role) || null;\n};\n\n/**\n * Check if user is developer\n */\nexport const isDeveloper = () => {\n  var _user$role;\n  const user = getCurrentUser();\n  return (user === null || user === void 0 ? void 0 : (_user$role = user.role) === null || _user$role === void 0 ? void 0 : _user$role.name) === 'developer';\n};\n\n/**\n * Get all user permissions\n */\nexport const getUserPermissions = () => {\n  var _user$role2;\n  const user = getCurrentUser();\n  return (user === null || user === void 0 ? void 0 : (_user$role2 = user.role) === null || _user$role2 === void 0 ? void 0 : _user$role2.permissions) || [];\n};\nvar _c;\n$RefreshReg$(_c, \"PermissionWrapper\");", "map": {"version": 3, "names": ["getCurrentUser", "userStr", "localStorage", "getItem", "JSON", "parse", "error", "console", "hasPermission", "permissionName", "user", "role", "name", "userPermissions", "permissions", "some", "permission", "hasAnyPermission", "permissionNames", "hasAllPermissions", "every", "getPermissionMessages", "create", "edit", "delete", "view", "general", "PermissionWrapper", "children", "fallback", "_c", "getUserRole", "isDeveloper", "_user$role", "getUserPermissions", "_user$role2", "$RefreshReg$"], "sources": ["D:/My Learning Projects/ucbs_v1_08/frontend/src/utils/permissions.js"], "sourcesContent": ["/**\n * Permission checking utilities for frontend\n */\n\n/**\n * Get current user from localStorage\n */\nexport const getCurrentUser = () => {\n  try {\n    const userStr = localStorage.getItem('currentUser');\n    return userStr ? JSON.parse(userStr) : null;\n  } catch (error) {\n    console.error('Error getting current user:', error);\n    return null;\n  }\n};\n\n/**\n * Check if user has a specific permission\n * @param {string} permissionName - The permission to check (e.g., 'customers.create')\n * @returns {boolean} - True if user has permission\n */\nexport const hasPermission = (permissionName) => {\n  const user = getCurrentUser();\n\n  // If no user, no permission\n  if (!user || !user.role) {\n    return false;\n  }\n\n  // Developer role has all permissions\n  if (user.role.name === 'developer') {\n    return true;\n  }\n\n  // Check if user has the specific permission\n  const userPermissions = user.role.permissions || [];\n  return userPermissions.some(permission => permission.name === permissionName);\n};\n\n/**\n * Check if user has any of the specified permissions\n * @param {string[]} permissionNames - Array of permissions to check\n * @returns {boolean} - True if user has any of the permissions\n */\nexport const hasAnyPermission = (permissionNames) => {\n  return permissionNames.some(permission => hasPermission(permission));\n};\n\n/**\n * Check if user has all of the specified permissions\n * @param {string[]} permissionNames - Array of permissions to check\n * @returns {boolean} - True if user has all permissions\n */\nexport const hasAllPermissions = (permissionNames) => {\n  return permissionNames.every(permission => hasPermission(permission));\n};\n\n/**\n * Get polite permission denied messages\n */\nexport const getPermissionMessages = () => ({\n  create: \"You don't have permission to create new items. Please contact your administrator if you need this access.\",\n  edit: \"You don't have permission to edit items. Please contact your administrator if you need this access.\",\n  delete: \"You don't have permission to delete items. Please contact your administrator if you need this access.\",\n  view: \"You don't have permission to view this information. Please contact your administrator if you need this access.\",\n  general: \"You don't have permission to perform this action. Please contact your administrator if you need this access.\"\n});\n\n/**\n * Permission-aware component wrapper\n * @param {string} permission - Required permission\n * @param {React.Component} children - Component to render if permission exists\n * @param {React.Component} fallback - Component to render if no permission (optional)\n */\nexport const PermissionWrapper = ({ permission, children, fallback = null }) => {\n  if (hasPermission(permission)) {\n    return children;\n  }\n  return fallback;\n};\n\n/**\n * Get user's role information\n */\nexport const getUserRole = () => {\n  const user = getCurrentUser();\n  return user?.role || null;\n};\n\n/**\n * Check if user is developer\n */\nexport const isDeveloper = () => {\n  const user = getCurrentUser();\n  return user?.role?.name === 'developer';\n};\n\n/**\n * Get all user permissions\n */\nexport const getUserPermissions = () => {\n  const user = getCurrentUser();\n  return user?.role?.permissions || [];\n};\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AACA;AACA;AACA,OAAO,MAAMA,cAAc,GAAGA,CAAA,KAAM;EAClC,IAAI;IACF,MAAMC,OAAO,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IACnD,OAAOF,OAAO,GAAGG,IAAI,CAACC,KAAK,CAACJ,OAAO,CAAC,GAAG,IAAI;EAC7C,CAAC,CAAC,OAAOK,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACnD,OAAO,IAAI;EACb;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAME,aAAa,GAAIC,cAAc,IAAK;EAC/C,MAAMC,IAAI,GAAGV,cAAc,CAAC,CAAC;;EAE7B;EACA,IAAI,CAACU,IAAI,IAAI,CAACA,IAAI,CAACC,IAAI,EAAE;IACvB,OAAO,KAAK;EACd;;EAEA;EACA,IAAID,IAAI,CAACC,IAAI,CAACC,IAAI,KAAK,WAAW,EAAE;IAClC,OAAO,IAAI;EACb;;EAEA;EACA,MAAMC,eAAe,GAAGH,IAAI,CAACC,IAAI,CAACG,WAAW,IAAI,EAAE;EACnD,OAAOD,eAAe,CAACE,IAAI,CAACC,UAAU,IAAIA,UAAU,CAACJ,IAAI,KAAKH,cAAc,CAAC;AAC/E,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMQ,gBAAgB,GAAIC,eAAe,IAAK;EACnD,OAAOA,eAAe,CAACH,IAAI,CAACC,UAAU,IAAIR,aAAa,CAACQ,UAAU,CAAC,CAAC;AACtE,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMG,iBAAiB,GAAID,eAAe,IAAK;EACpD,OAAOA,eAAe,CAACE,KAAK,CAACJ,UAAU,IAAIR,aAAa,CAACQ,UAAU,CAAC,CAAC;AACvE,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMK,qBAAqB,GAAGA,CAAA,MAAO;EAC1CC,MAAM,EAAE,2GAA2G;EACnHC,IAAI,EAAE,qGAAqG;EAC3GC,MAAM,EAAE,uGAAuG;EAC/GC,IAAI,EAAE,gHAAgH;EACtHC,OAAO,EAAE;AACX,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,iBAAiB,GAAGA,CAAC;EAAEX,UAAU;EAAEY,QAAQ;EAAEC,QAAQ,GAAG;AAAK,CAAC,KAAK;EAC9E,IAAIrB,aAAa,CAACQ,UAAU,CAAC,EAAE;IAC7B,OAAOY,QAAQ;EACjB;EACA,OAAOC,QAAQ;AACjB,CAAC;;AAED;AACA;AACA;AAFAC,EAAA,GAPaH,iBAAiB;AAU9B,OAAO,MAAMI,WAAW,GAAGA,CAAA,KAAM;EAC/B,MAAMrB,IAAI,GAAGV,cAAc,CAAC,CAAC;EAC7B,OAAO,CAAAU,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC,IAAI,KAAI,IAAI;AAC3B,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMqB,WAAW,GAAGA,CAAA,KAAM;EAAA,IAAAC,UAAA;EAC/B,MAAMvB,IAAI,GAAGV,cAAc,CAAC,CAAC;EAC7B,OAAO,CAAAU,IAAI,aAAJA,IAAI,wBAAAuB,UAAA,GAAJvB,IAAI,CAAEC,IAAI,cAAAsB,UAAA,uBAAVA,UAAA,CAAYrB,IAAI,MAAK,WAAW;AACzC,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMsB,kBAAkB,GAAGA,CAAA,KAAM;EAAA,IAAAC,WAAA;EACtC,MAAMzB,IAAI,GAAGV,cAAc,CAAC,CAAC;EAC7B,OAAO,CAAAU,IAAI,aAAJA,IAAI,wBAAAyB,WAAA,GAAJzB,IAAI,CAAEC,IAAI,cAAAwB,WAAA,uBAAVA,WAAA,CAAYrB,WAAW,KAAI,EAAE;AACtC,CAAC;AAAC,IAAAgB,EAAA;AAAAM,YAAA,CAAAN,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}