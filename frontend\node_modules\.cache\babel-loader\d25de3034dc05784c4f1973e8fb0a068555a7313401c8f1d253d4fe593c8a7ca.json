{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"1\",\n  opacity: \".3\"\n}, \"0\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M9 12c0 1.66 1.34 3 3 3s3-1.34 3-3-1.34-3-3-3-3 1.34-3 3m4 0c0 .55-.45 1-1 1s-1-.45-1-1 .45-1 1-1 1 .45 1 1\"\n}, \"1\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M8 8H5.09C6.47 5.61 9.05 4 12 4c3.72 0 6.85 2.56 7.74 6h2.06c-.93-4.56-4.96-8-9.8-8-3.27 0-6.18 1.58-8 4.01V4H2v6h6zm8 6v2h2.91c-1.38 2.39-3.96 4-6.91 4-3.72 0-6.85-2.56-7.74-6H2.2c.93 4.56 4.96 8 9.8 8 3.27 0 6.18-1.58 8-4.01V20h2v-6z\"\n}, \"2\")], 'FlipCameraAndroidTwoTone');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "cx", "cy", "r", "opacity", "d"], "sources": ["D:/My Learning Projects/ucbs_v1_08/frontend/node_modules/@mui/icons-material/esm/FlipCameraAndroidTwoTone.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"1\",\n  opacity: \".3\"\n}, \"0\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M9 12c0 1.66 1.34 3 3 3s3-1.34 3-3-1.34-3-3-3-3 1.34-3 3m4 0c0 .55-.45 1-1 1s-1-.45-1-1 .45-1 1-1 1 .45 1 1\"\n}, \"1\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M8 8H5.09C6.47 5.61 9.05 4 12 4c3.72 0 6.85 2.56 7.74 6h2.06c-.93-4.56-4.96-8-9.8-8-3.27 0-6.18 1.58-8 4.01V4H2v6h6zm8 6v2h2.91c-1.38 2.39-3.96 4-6.91 4-3.72 0-6.85-2.56-7.74-6H2.2c.93 4.56 4.96 8 9.8 8 3.27 0 6.18-1.58 8-4.01V20h2v-6z\"\n}, \"2\")], 'FlipCameraAndroidTwoTone');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,uBAAuB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAC,CAAC,aAAaE,IAAI,CAAC,QAAQ,EAAE;EACxDC,EAAE,EAAE,IAAI;EACRC,EAAE,EAAE,IAAI;EACRC,CAAC,EAAE,GAAG;EACNC,OAAO,EAAE;AACX,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaJ,IAAI,CAAC,MAAM,EAAE;EACjCK,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaL,IAAI,CAAC,MAAM,EAAE;EACjCK,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,0BAA0B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}