{"ast": null, "code": "import * as C from './EncoderConstants';\nimport Arrays from '../../util/Arrays';\nimport StringUtils from '../../common/StringUtils';\nexport function static_CHAR_MAP(CHAR_MAP) {\n  var spaceCharCode = StringUtils.getCharCode(' ');\n  var pointCharCode = StringUtils.getCharCode('.');\n  var commaCharCode = StringUtils.getCharCode(',');\n  CHAR_MAP[C.MODE_UPPER][spaceCharCode] = 1;\n  var zUpperCharCode = StringUtils.getCharCode('Z');\n  var aUpperCharCode = StringUtils.getCharCode('A');\n  for (var c = aUpperCharCode; c <= zUpperCharCode; c++) {\n    CHAR_MAP[C.MODE_UPPER][c] = c - aUpperCharCode + 2;\n  }\n  CHAR_MAP[C.MODE_LOWER][spaceCharCode] = 1;\n  var zLowerCharCode = StringUtils.getCharCode('z');\n  var aLowerCharCode = StringUtils.getCharCode('a');\n  for (var c = aLowerCharCode; c <= zLowerCharCode; c++) {\n    CHAR_MAP[C.MODE_LOWER][c] = c - aLowerCharCode + 2;\n  }\n  CHAR_MAP[C.MODE_DIGIT][spaceCharCode] = 1;\n  var nineCharCode = StringUtils.getCharCode('9');\n  var zeroCharCode = StringUtils.getCharCode('0');\n  for (var c = zeroCharCode; c <= nineCharCode; c++) {\n    CHAR_MAP[C.MODE_DIGIT][c] = c - zeroCharCode + 2;\n  }\n  CHAR_MAP[C.MODE_DIGIT][commaCharCode] = 12;\n  CHAR_MAP[C.MODE_DIGIT][pointCharCode] = 13;\n  var mixedTable = ['\\x00', ' ', '\\x01', '\\x02', '\\x03', '\\x04', '\\x05', '\\x06', '\\x07', '\\b', '\\t', '\\n', '\\x0b', '\\f', '\\r', '\\x1b', '\\x1c', '\\x1d', '\\x1e', '\\x1f', '@', '\\\\', '^', '_', '`', '|', '~', '\\x7f'];\n  for (var i = 0; i < mixedTable.length; i++) {\n    CHAR_MAP[C.MODE_MIXED][StringUtils.getCharCode(mixedTable[i])] = i;\n  }\n  var punctTable = ['\\x00', '\\r', '\\x00', '\\x00', '\\x00', '\\x00', '!', '\\'', '#', '$', '%', '&', '\\'', '(', ')', '*', '+', ',', '-', '.', '/', ':', ';', '<', '=', '>', '?', '[', ']', '{', '}'];\n  for (var i = 0; i < punctTable.length; i++) {\n    if (StringUtils.getCharCode(punctTable[i]) > 0) {\n      CHAR_MAP[C.MODE_PUNCT][StringUtils.getCharCode(punctTable[i])] = i;\n    }\n  }\n  return CHAR_MAP;\n}\nexport var CHAR_MAP = static_CHAR_MAP(Arrays.createInt32Array(5, 256));", "map": {"version": 3, "names": ["C", "<PERSON><PERSON><PERSON>", "StringUtils", "static_CHAR_MAP", "CHAR_MAP", "spaceCharCode", "getCharCode", "pointCharCode", "commaCharCode", "MODE_UPPER", "zUpperCharCode", "aUpperCharCode", "c", "MODE_LOWER", "zLowerCharCode", "aLowerCharCode", "MODE_DIGIT", "nineCharCode", "zeroCharCode", "mixedTable", "i", "length", "MODE_MIXED", "punctTable", "MODE_PUNCT", "createInt32Array"], "sources": ["D:/My Learning Projects/ucbs_v1_08/frontend/node_modules/@zxing/library/esm/core/aztec/encoder/CharMap.js"], "sourcesContent": ["import * as C from './EncoderConstants';\nimport Arrays from '../../util/Arrays';\nimport StringUtils from '../../common/StringUtils';\nexport function static_CHAR_MAP(CHAR_MAP) {\n    var spaceCharCode = StringUtils.getCharCode(' ');\n    var pointCharCode = StringUtils.getCharCode('.');\n    var commaCharCode = StringUtils.getCharCode(',');\n    CHAR_MAP[C.MODE_UPPER][spaceCharCode] = 1;\n    var zUpperCharCode = StringUtils.getCharCode('Z');\n    var aUpperCharCode = StringUtils.getCharCode('A');\n    for (var c = aUpperCharCode; c <= zUpperCharCode; c++) {\n        CHAR_MAP[C.MODE_UPPER][c] = c - aUpperCharCode + 2;\n    }\n    CHAR_MAP[C.MODE_LOWER][spaceCharCode] = 1;\n    var zLowerCharCode = StringUtils.getCharCode('z');\n    var aLowerCharCode = StringUtils.getCharCode('a');\n    for (var c = aLowerCharCode; c <= zLowerCharCode; c++) {\n        CHAR_MAP[C.MODE_LOWER][c] = c - aLowerCharCode + 2;\n    }\n    CHAR_MAP[C.MODE_DIGIT][spaceCharCode] = 1;\n    var nineCharCode = StringUtils.getCharCode('9');\n    var zeroCharCode = StringUtils.getCharCode('0');\n    for (var c = zeroCharCode; c <= nineCharCode; c++) {\n        CHAR_MAP[C.MODE_DIGIT][c] = c - zeroCharCode + 2;\n    }\n    CHAR_MAP[C.MODE_DIGIT][commaCharCode] = 12;\n    CHAR_MAP[C.MODE_DIGIT][pointCharCode] = 13;\n    var mixedTable = [\n        '\\x00',\n        ' ',\n        '\\x01',\n        '\\x02',\n        '\\x03',\n        '\\x04',\n        '\\x05',\n        '\\x06',\n        '\\x07',\n        '\\b',\n        '\\t',\n        '\\n',\n        '\\x0b',\n        '\\f',\n        '\\r',\n        '\\x1b',\n        '\\x1c',\n        '\\x1d',\n        '\\x1e',\n        '\\x1f',\n        '@',\n        '\\\\',\n        '^',\n        '_',\n        '`',\n        '|',\n        '~',\n        '\\x7f'\n    ];\n    for (var i = 0; i < mixedTable.length; i++) {\n        CHAR_MAP[C.MODE_MIXED][StringUtils.getCharCode(mixedTable[i])] = i;\n    }\n    var punctTable = [\n        '\\x00',\n        '\\r',\n        '\\x00',\n        '\\x00',\n        '\\x00',\n        '\\x00',\n        '!',\n        '\\'',\n        '#',\n        '$',\n        '%',\n        '&',\n        '\\'',\n        '(',\n        ')',\n        '*',\n        '+',\n        ',',\n        '-',\n        '.',\n        '/',\n        ':',\n        ';',\n        '<',\n        '=',\n        '>',\n        '?',\n        '[',\n        ']',\n        '{',\n        '}'\n    ];\n    for (var i = 0; i < punctTable.length; i++) {\n        if (StringUtils.getCharCode(punctTable[i]) > 0) {\n            CHAR_MAP[C.MODE_PUNCT][StringUtils.getCharCode(punctTable[i])] = i;\n        }\n    }\n    return CHAR_MAP;\n}\nexport var CHAR_MAP = static_CHAR_MAP(Arrays.createInt32Array(5, 256));\n"], "mappings": "AAAA,OAAO,KAAKA,CAAC,MAAM,oBAAoB;AACvC,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAO,SAASC,eAAeA,CAACC,QAAQ,EAAE;EACtC,IAAIC,aAAa,GAAGH,WAAW,CAACI,WAAW,CAAC,GAAG,CAAC;EAChD,IAAIC,aAAa,GAAGL,WAAW,CAACI,WAAW,CAAC,GAAG,CAAC;EAChD,IAAIE,aAAa,GAAGN,WAAW,CAACI,WAAW,CAAC,GAAG,CAAC;EAChDF,QAAQ,CAACJ,CAAC,CAACS,UAAU,CAAC,CAACJ,aAAa,CAAC,GAAG,CAAC;EACzC,IAAIK,cAAc,GAAGR,WAAW,CAACI,WAAW,CAAC,GAAG,CAAC;EACjD,IAAIK,cAAc,GAAGT,WAAW,CAACI,WAAW,CAAC,GAAG,CAAC;EACjD,KAAK,IAAIM,CAAC,GAAGD,cAAc,EAAEC,CAAC,IAAIF,cAAc,EAAEE,CAAC,EAAE,EAAE;IACnDR,QAAQ,CAACJ,CAAC,CAACS,UAAU,CAAC,CAACG,CAAC,CAAC,GAAGA,CAAC,GAAGD,cAAc,GAAG,CAAC;EACtD;EACAP,QAAQ,CAACJ,CAAC,CAACa,UAAU,CAAC,CAACR,aAAa,CAAC,GAAG,CAAC;EACzC,IAAIS,cAAc,GAAGZ,WAAW,CAACI,WAAW,CAAC,GAAG,CAAC;EACjD,IAAIS,cAAc,GAAGb,WAAW,CAACI,WAAW,CAAC,GAAG,CAAC;EACjD,KAAK,IAAIM,CAAC,GAAGG,cAAc,EAAEH,CAAC,IAAIE,cAAc,EAAEF,CAAC,EAAE,EAAE;IACnDR,QAAQ,CAACJ,CAAC,CAACa,UAAU,CAAC,CAACD,CAAC,CAAC,GAAGA,CAAC,GAAGG,cAAc,GAAG,CAAC;EACtD;EACAX,QAAQ,CAACJ,CAAC,CAACgB,UAAU,CAAC,CAACX,aAAa,CAAC,GAAG,CAAC;EACzC,IAAIY,YAAY,GAAGf,WAAW,CAACI,WAAW,CAAC,GAAG,CAAC;EAC/C,IAAIY,YAAY,GAAGhB,WAAW,CAACI,WAAW,CAAC,GAAG,CAAC;EAC/C,KAAK,IAAIM,CAAC,GAAGM,YAAY,EAAEN,CAAC,IAAIK,YAAY,EAAEL,CAAC,EAAE,EAAE;IAC/CR,QAAQ,CAACJ,CAAC,CAACgB,UAAU,CAAC,CAACJ,CAAC,CAAC,GAAGA,CAAC,GAAGM,YAAY,GAAG,CAAC;EACpD;EACAd,QAAQ,CAACJ,CAAC,CAACgB,UAAU,CAAC,CAACR,aAAa,CAAC,GAAG,EAAE;EAC1CJ,QAAQ,CAACJ,CAAC,CAACgB,UAAU,CAAC,CAACT,aAAa,CAAC,GAAG,EAAE;EAC1C,IAAIY,UAAU,GAAG,CACb,MAAM,EACN,GAAG,EACH,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,MAAM,EACN,IAAI,EACJ,IAAI,EACJ,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,GAAG,EACH,IAAI,EACJ,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,MAAM,CACT;EACD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,UAAU,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;IACxChB,QAAQ,CAACJ,CAAC,CAACsB,UAAU,CAAC,CAACpB,WAAW,CAACI,WAAW,CAACa,UAAU,CAACC,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC;EACtE;EACA,IAAIG,UAAU,GAAG,CACb,MAAM,EACN,IAAI,EACJ,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,GAAG,EACH,IAAI,EACJ,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,IAAI,EACJ,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,CACN;EACD,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGG,UAAU,CAACF,MAAM,EAAED,CAAC,EAAE,EAAE;IACxC,IAAIlB,WAAW,CAACI,WAAW,CAACiB,UAAU,CAACH,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;MAC5ChB,QAAQ,CAACJ,CAAC,CAACwB,UAAU,CAAC,CAACtB,WAAW,CAACI,WAAW,CAACiB,UAAU,CAACH,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC;IACtE;EACJ;EACA,OAAOhB,QAAQ;AACnB;AACA,OAAO,IAAIA,QAAQ,GAAGD,eAAe,CAACF,MAAM,CAACwB,gBAAgB,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}