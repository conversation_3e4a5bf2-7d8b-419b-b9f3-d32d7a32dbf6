{"ast": null, "code": "export const filterBreakpointKeys = (breakpointsKeys, responsiveKeys) => breakpointsKeys.filter(key => responsiveKeys.includes(key));\nexport const traverseBreakpoints = (breakpoints, responsive, iterator) => {\n  const smallestBreakpoint = breakpoints.keys[0]; // the keys is sorted from smallest to largest by `createBreakpoints`.\n\n  if (Array.isArray(responsive)) {\n    responsive.forEach((breakpointValue, index) => {\n      iterator((responsiveStyles, style) => {\n        if (index <= breakpoints.keys.length - 1) {\n          if (index === 0) {\n            Object.assign(responsiveStyles, style);\n          } else {\n            responsiveStyles[breakpoints.up(breakpoints.keys[index])] = style;\n          }\n        }\n      }, breakpointValue);\n    });\n  } else if (responsive && typeof responsive === 'object') {\n    // prevent null\n    // responsive could be a very big object, pick the smallest responsive values\n\n    const keys = Object.keys(responsive).length > breakpoints.keys.length ? breakpoints.keys : filterBreakpointKeys(breakpoints.keys, Object.keys(responsive));\n    keys.forEach(key => {\n      if (breakpoints.keys.indexOf(key) !== -1) {\n        // @ts-ignore already checked that responsive is an object\n        const breakpointValue = responsive[key];\n        if (breakpointValue !== undefined) {\n          iterator((responsiveStyles, style) => {\n            if (smallestBreakpoint === key) {\n              Object.assign(responsiveStyles, style);\n            } else {\n              responsiveStyles[breakpoints.up(key)] = style;\n            }\n          }, breakpointValue);\n        }\n      }\n    });\n  } else if (typeof responsive === 'number' || typeof responsive === 'string') {\n    iterator((responsiveStyles, style) => {\n      Object.assign(responsiveStyles, style);\n    }, responsive);\n  }\n};", "map": {"version": 3, "names": ["filterBreakpointKeys", "breakpointsKeys", "responsiveKeys", "filter", "key", "includes", "traverseBreakpoints", "breakpoints", "responsive", "iterator", "smallestBreakpoint", "keys", "Array", "isArray", "for<PERSON>ach", "breakpoint<PERSON><PERSON>ue", "index", "responsiveStyles", "style", "length", "Object", "assign", "up", "indexOf", "undefined"], "sources": ["D:/My Learning Projects/ucbs_v1_08/frontend/node_modules/@mui/system/esm/Unstable_Grid/traverseBreakpoints.js"], "sourcesContent": ["export const filterBreakpointKeys = (breakpointsKeys, responsiveKeys) => breakpointsKeys.filter(key => responsiveKeys.includes(key));\nexport const traverseBreakpoints = (breakpoints, responsive, iterator) => {\n  const smallestBreakpoint = breakpoints.keys[0]; // the keys is sorted from smallest to largest by `createBreakpoints`.\n\n  if (Array.isArray(responsive)) {\n    responsive.forEach((breakpointValue, index) => {\n      iterator((responsiveStyles, style) => {\n        if (index <= breakpoints.keys.length - 1) {\n          if (index === 0) {\n            Object.assign(responsiveStyles, style);\n          } else {\n            responsiveStyles[breakpoints.up(breakpoints.keys[index])] = style;\n          }\n        }\n      }, breakpointValue);\n    });\n  } else if (responsive && typeof responsive === 'object') {\n    // prevent null\n    // responsive could be a very big object, pick the smallest responsive values\n\n    const keys = Object.keys(responsive).length > breakpoints.keys.length ? breakpoints.keys : filterBreakpointKeys(breakpoints.keys, Object.keys(responsive));\n    keys.forEach(key => {\n      if (breakpoints.keys.indexOf(key) !== -1) {\n        // @ts-ignore already checked that responsive is an object\n        const breakpointValue = responsive[key];\n        if (breakpointValue !== undefined) {\n          iterator((responsiveStyles, style) => {\n            if (smallestBreakpoint === key) {\n              Object.assign(responsiveStyles, style);\n            } else {\n              responsiveStyles[breakpoints.up(key)] = style;\n            }\n          }, breakpointValue);\n        }\n      }\n    });\n  } else if (typeof responsive === 'number' || typeof responsive === 'string') {\n    iterator((responsiveStyles, style) => {\n      Object.assign(responsiveStyles, style);\n    }, responsive);\n  }\n};"], "mappings": "AAAA,OAAO,MAAMA,oBAAoB,GAAGA,CAACC,eAAe,EAAEC,cAAc,KAAKD,eAAe,CAACE,MAAM,CAACC,GAAG,IAAIF,cAAc,CAACG,QAAQ,CAACD,GAAG,CAAC,CAAC;AACpI,OAAO,MAAME,mBAAmB,GAAGA,CAACC,WAAW,EAAEC,UAAU,EAAEC,QAAQ,KAAK;EACxE,MAAMC,kBAAkB,GAAGH,WAAW,CAACI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEhD,IAAIC,KAAK,CAACC,OAAO,CAACL,UAAU,CAAC,EAAE;IAC7BA,UAAU,CAACM,OAAO,CAAC,CAACC,eAAe,EAAEC,KAAK,KAAK;MAC7CP,QAAQ,CAAC,CAACQ,gBAAgB,EAAEC,KAAK,KAAK;QACpC,IAAIF,KAAK,IAAIT,WAAW,CAACI,IAAI,CAACQ,MAAM,GAAG,CAAC,EAAE;UACxC,IAAIH,KAAK,KAAK,CAAC,EAAE;YACfI,MAAM,CAACC,MAAM,CAACJ,gBAAgB,EAAEC,KAAK,CAAC;UACxC,CAAC,MAAM;YACLD,gBAAgB,CAACV,WAAW,CAACe,EAAE,CAACf,WAAW,CAACI,IAAI,CAACK,KAAK,CAAC,CAAC,CAAC,GAAGE,KAAK;UACnE;QACF;MACF,CAAC,EAAEH,eAAe,CAAC;IACrB,CAAC,CAAC;EACJ,CAAC,MAAM,IAAIP,UAAU,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;IACvD;IACA;;IAEA,MAAMG,IAAI,GAAGS,MAAM,CAACT,IAAI,CAACH,UAAU,CAAC,CAACW,MAAM,GAAGZ,WAAW,CAACI,IAAI,CAACQ,MAAM,GAAGZ,WAAW,CAACI,IAAI,GAAGX,oBAAoB,CAACO,WAAW,CAACI,IAAI,EAAES,MAAM,CAACT,IAAI,CAACH,UAAU,CAAC,CAAC;IAC1JG,IAAI,CAACG,OAAO,CAACV,GAAG,IAAI;MAClB,IAAIG,WAAW,CAACI,IAAI,CAACY,OAAO,CAACnB,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;QACxC;QACA,MAAMW,eAAe,GAAGP,UAAU,CAACJ,GAAG,CAAC;QACvC,IAAIW,eAAe,KAAKS,SAAS,EAAE;UACjCf,QAAQ,CAAC,CAACQ,gBAAgB,EAAEC,KAAK,KAAK;YACpC,IAAIR,kBAAkB,KAAKN,GAAG,EAAE;cAC9BgB,MAAM,CAACC,MAAM,CAACJ,gBAAgB,EAAEC,KAAK,CAAC;YACxC,CAAC,MAAM;cACLD,gBAAgB,CAACV,WAAW,CAACe,EAAE,CAAClB,GAAG,CAAC,CAAC,GAAGc,KAAK;YAC/C;UACF,CAAC,EAAEH,eAAe,CAAC;QACrB;MACF;IACF,CAAC,CAAC;EACJ,CAAC,MAAM,IAAI,OAAOP,UAAU,KAAK,QAAQ,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;IAC3EC,QAAQ,CAAC,CAACQ,gBAAgB,EAAEC,KAAK,KAAK;MACpCE,MAAM,CAACC,MAAM,CAACJ,gBAAgB,EAAEC,KAAK,CAAC;IACxC,CAAC,EAAEV,UAAU,CAAC;EAChB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}